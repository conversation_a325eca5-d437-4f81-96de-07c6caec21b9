<?php

namespace App\Models;

use CodeIgniter\Model;

class PgBranchProfileModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'tb_autopay_pg_branch_profile';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $insertID         = 0;
    protected $returnType       = 'object';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'pg_merchant_id',
        'pg_branch_id',
        'pg_payment_method_id',
        'pg_branch_terminal_id',
        'pg_profile_id',
        'bank_account_id',
        'bank_sub_account_id',
        'use_dynamic_va',
        'company_id',
    ];
}
