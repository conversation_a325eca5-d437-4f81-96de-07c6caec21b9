<?php

use App\Models\BankBonusModel;
use App\Models\CounterModel;

function add_user_log($data) {
    if(!is_array($data))
        return FALSE;

    $userLogModel = new \App\Models\UserLogModel();

    $result = $userLogModel->insert($data);
    
    return $result;
}

function check_logged_user() {
    $session = session();

    if(isset($_SESSION['user_logged_in']['user_id'])) { 
        return TRUE;
    } else 
        return FALSE;
}

function check_logged_admin() {
    $session = session();

    if(isset($_SESSION['admin_logged_in']['email'])) { 
        return TRUE;
    } else 
        return FALSE;
}

function get_user_detail($user_id) {
    if(!is_numeric($user_id))
        return FALSE;

    $userModel = new \App\Models\UserModel();

    $result = $userModel->find($user_id);
    
    return $result;
}

function get_logged_user_detail() {
    $session = session();

    if(isset($_SESSION['user_logged_in']['user_id'])) { 
        $user_id = $_SESSION['user_logged_in']['user_id'];

        if(!is_numeric($user_id))
            return FALSE;

        $result = get_user_detail($user_id);
        
        return $result;
    } else 
        return FALSE;
}

function get_gravatar( $email, $s = 150, $d = 'identicon', $r = 'g', $img = false, $atts = array() ) {
    $url = 'https://www.gravatar.com/avatar/';
    $url .= md5( strtolower( trim( $email ) ) );
    $url .= "?s=$s&d=$d&r=$r";
    if ( $img ) {
        $url = '<img src="' . $url . '"';
        foreach ( $atts as $key => $val )
            $url .= ' ' . $key . '="' . $val . '"';
        $url .= ' />';
    }
    return $url;
}

function to_slug($str, $separator='-', $lowercase=false) {
    $str = trim(mb_strtolower($str));
    $str = preg_replace('/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/', 'a', $str);
    $str = preg_replace('/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/', 'e', $str);
    $str = preg_replace('/(ì|í|ị|ỉ|ĩ)/', 'i', $str);
    $str = preg_replace('/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/', 'o', $str);
    $str = preg_replace('/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/', 'u', $str);
    $str = preg_replace('/(ỳ|ý|ỵ|ỷ|ỹ)/', 'y', $str);
    $str = preg_replace('/(đ)/', 'd', $str);
    $str = preg_replace('/(_)/', $separator, $str);
    $str = preg_replace('/[^a-z0-9-\s]/', '', $str);
    $str = preg_replace('/([\s]+)/', $separator, $str);
    if($lowercase)
        return strtolower($str);
    else
        return $str;
} 

function remove_accents($str, $uppercase=false) {
    $str = trim(mb_strtolower($str));
    $str = preg_replace('/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/', 'a', $str);
    $str = preg_replace('/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/', 'e', $str);
    $str = preg_replace('/(ì|í|ị|ỉ|ĩ)/', 'i', $str);
    $str = preg_replace('/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/', 'o', $str);
    $str = preg_replace('/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/', 'u', $str);
    $str = preg_replace('/(ỳ|ý|ỵ|ỷ|ỹ)/', 'y', $str);
    $str = preg_replace('/(đ)/', 'd', $str);
    $str = preg_replace('/[^a-z0-9-\s]/', '', $str);
    if($uppercase)
        return strtoupper($str);
    else
        return $str;
} 

function timespan($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => 'năm',
        'm' => 'tháng',
        'w' => 'tuần',
        'd' => 'ngày',
        'h' => 'giờ',
        'i' => 'phút',
        's' => 'giây',
    );
    foreach ($string as $k => &$v) {
        if ($diff->$k) {
            $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? '' : '');
        } else {
            unset($string[$k]);
        }
    }

    if ($full) $string = array_slice($string, 0, $full);
    return $string ? implode(', ', $string) . ' trước' : 'vừa mới';
}

 
function show_404() {
    throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
}
  
function has_permission($permission, $can, $user_id=FALSE) {
    if ($permission === 'NotificationViber') {
        $permission = 'NotificationTelegram';
    }
    
    $session = session();
    if (!$session->get('user_logged_in'))
        return FALSE;

    $user_session = $session->get('user_logged_in');

    // this case check for current logged user
    if($user_id === FALSE) {
        if(!isset($user_session['user_id']) || !isset($user_session['company_id']))
            return FALSE;
        $user_id = $user_session['user_id'];

    } 

    
    // check SuperAdmin or Admin
    $companyUserModel = new \App\Models\CompanyUserModel();

    $company_user_result = $companyUserModel->select('role')->where(['user_id' => $user_id,'company_id' => $user_session['company_id']])->get()->getRow();

    if(!is_object($company_user_result))
        return FALSE;

        /*
    $companyModel = new \App\Models\CompanyModel();

    $company_result = $companyModel->where(['id' => $user_session['company_id']])->get()->getRow();

    if($company_result->status != 'Active')
        return FALSE; */

    if($company_user_result->role == 'SuperAdmin' || $company_user_result->role == 'Admin')
        return TRUE;

    // normal user
    $userPermissionFeatureModel = new \App\Models\UserPermissionFeatureModel();

    $result = $userPermissionFeatureModel->where(['user_id' => $user_id,'company_id' => $user_session['company_id'], 'feature_slug' => $permission, $can => 1])->countAllResults();
    
    if($result == 1)
        return TRUE;
    else
        return FALSE;
 
}

function has_bank_permission($bank_account_id, $user_id=FALSE) {

    $session = session();
    if (!$session->get('user_logged_in'))
        return FALSE;

    $user_session = $session->get('user_logged_in');

    // this case check for current logged user
    if($user_id === FALSE) {
        if(!isset($user_session['user_id']) || !isset($user_session['company_id']))
            return FALSE;
        $user_id = $user_session['user_id'];

    } 
    
    // check SuperAdmin or Admin
    $companyUserModel = new \App\Models\CompanyUserModel();

    $company_result = $companyUserModel->select('role')->where(['user_id' => $user_id,'company_id' => $user_session['company_id']])->get()->getRow();

    if(!is_object($company_result))
        return FALSE;

    if($company_result->role == 'SuperAdmin' || $company_result->role == 'Admin')
        return TRUE;

    // normal user
    $userPermissionBankModel = new \App\Models\UserPermissionBankModel();

    $bank_permissions = $userPermissionBankModel->where(['user_id' => $user_id,'bank_account_id'=>$bank_account_id])->orderBy('bank_account_id', 'desc')->get()->getResult();
    
    if(count($bank_permissions) == 0)
        return FALSE;
    else
        return TRUE;
 
}


function number_to_words($number) {
 
    $hyphen      = ' ';
    $conjunction = ' ';
    $separator   = ' ';
    $negative    = 'âm ';
    $decimal     = ' phẩy ';
    $one		 = 'mốt';
    $ten         = 'lẻ';
    $dictionary  = array(
    0                   => 'Không',
    1                   => 'Một',
    2                   => 'Hai',
    3                   => 'Ba',
    4                   => 'Bốn',
    5                   => 'Năm',
    6                   => 'Sáu',
    7                   => 'Bảy',
    8                   => 'Tám',
    9                   => 'Chín',
    10                  => 'Mười',
    11                  => 'Mười một',
    12                  => 'Mười hai',
    13                  => 'Mười ba',
    14                  => 'Mười bốn',
    15                  => 'Mười lăm',
    16                  => 'Mười sáu',
    17                  => 'Mười bảy',
    18                  => 'Mười tám',
    19                  => 'Mười chín',
    20                  => 'Hai mươi',
    30                  => 'Ba mươi',
    40                  => 'Bốn mươi',
    50                  => 'Năm mươi',
    60                  => 'Sáu mươi',
    70                  => 'Bảy mươi',
    80                  => 'Tám mươi',
    90                  => 'Chín mươi',
    100                 => 'trăm',
    1000                => 'ngàn',
    1000000             => 'triệu',
    1000000000          => 'tỷ',
    1000000000000       => 'nghìn tỷ',
    1000000000000000    => 'ngàn triệu triệu',
    1000000000000000000 => 'tỷ tỷ'
    );
     
    if (!is_numeric($number)) {
        return false;
    }
     
     
    if ($number < 0) {
        return $negative . number_to_words(abs($number));
    }
     
    $string = $fraction = null;
     
    if (strpos($number, '.') !== false) {
        list($number, $fraction) = explode('.', $number);
    }
     
    switch (true) {
        case $number < 21:
            $string = $dictionary[$number];
        break;
        case $number < 100:
            $tens   = ((int) ($number / 10)) * 10;
            $units  = $number % 10;
            $string = $dictionary[$tens];
            if ($units) {
                $string .= strtolower( $hyphen . ($units==1?$one:$dictionary[$units]) );
            }
        break;
        case $number < 1000:
            $hundreds  = $number / 100;
            $remainder = $number % 100;
            $string = $dictionary[$hundreds] . ' ' . $dictionary[100];
            if ($remainder) {
                $string .= strtolower( $conjunction . ($remainder<10?$ten.$hyphen:null) . number_to_words($remainder) );
            }
        break;
        default:
            $baseUnit = pow(1000, floor(log($number, 1000)));
            $numBaseUnits = (int) ($number / $baseUnit);
            $remainder = $number - ($numBaseUnits*$baseUnit);
            $string = number_to_words($numBaseUnits) . ' ' . $dictionary[$baseUnit];
            if ($remainder) {
                $string .= strtolower( $remainder < 100 ? $conjunction : $separator );
                $string .= strtolower( number_to_words($remainder) );
            }
        break;
    }
     
    if (null !== $fraction && is_numeric($fraction)) {
        $string .= $decimal;
        $words = array();
        foreach (str_split((string) $fraction) as $number) {
            $words[] = $dictionary[$number];
        }
        $string .= implode(' ', $words);
    }
     
    return $string;
}

function is_mobile() {
    $request = \Config\Services::request();
    $agent = $request->getUserAgent();
    if ($agent->isMobile())
        return TRUE;
    else
        return FALSE;
}

/**
 * Return the first day of the Week/Month/Quarter/Year that the
 * current/provided date falls within
 *
 * @param string   $period The period to find the first day of. ('year', 'quarter', 'month', 'week')
 * @param DateTime $date   The date to use instead of the current date
 *
 * @return DateTime
 * @throws InvalidArgumentException
 */
function firstDayOf($period, DateTime $date = null)
{
    $period = strtolower($period);
    $validPeriods = array('year', 'quarter', 'month', 'week');

    if ( ! in_array($period, $validPeriods))
        throw new InvalidArgumentException('Period must be one of: ' . implode(', ', $validPeriods));

    $newDate = ($date === null) ? new DateTime() : clone $date;

    switch ($period) {
        case 'year':
            $newDate->modify('first day of january ' . $newDate->format('Y'));
            break;
        case 'quarter':
            $month = $newDate->format('n') ;

            if ($month < 4) {
                $newDate->modify('first day of january ' . $newDate->format('Y'));
            } elseif ($month > 3 && $month < 7) {
                $newDate->modify('first day of april ' . $newDate->format('Y'));
            } elseif ($month > 6 && $month < 10) {
                $newDate->modify('first day of july ' . $newDate->format('Y'));
            } elseif ($month > 9) {
                $newDate->modify('first day of october ' . $newDate->format('Y'));
            }
            break;
        case 'month':
            $newDate->modify('first day of this month');
            break;
        case 'week':
            $newDate->modify(($newDate->format('w') === '0') ? 'monday last week' : 'monday this week');
            break;
    }

    return $newDate;
}

/**
 * Return the last day of the Week/Month/Quarter/Year that the
 * current/provided date falls within
 *
 * @param string   $period The period to find the last day of. ('year', 'quarter', 'month', 'week')
 * @param DateTime $date   The date to use instead of the current date
 *
 * @return DateTime
 * @throws InvalidArgumentException
 */
function lastDayOf($period, DateTime $date = null)
{
    $period = strtolower($period);
    $validPeriods = array('year', 'quarter', 'month', 'week');
 
    if ( ! in_array($period, $validPeriods))
        throw new InvalidArgumentException('Period must be one of: ' . implode(', ', $validPeriods));
 
    $newDate = ($date === null) ? new DateTime() : clone $date;
 
    switch ($period)
    {
        case 'year':
            $newDate->modify('last day of december ' . $newDate->format('Y'));
            break;
        case 'quarter':
            $month = $newDate->format('n') ;
 
            if ($month < 4) {
                $newDate->modify('last day of march ' . $newDate->format('Y'));
            } elseif ($month > 3 && $month < 7) {
                $newDate->modify('last day of june ' . $newDate->format('Y'));
            } elseif ($month > 6 && $month < 10) {
                $newDate->modify('last day of september ' . $newDate->format('Y'));
            } elseif ($month > 9) {
                $newDate->modify('last day of december ' . $newDate->format('Y'));
            }
            break;
        case 'month':
            $newDate->modify('last day of this month');
            break;
        case 'week':
            $newDate->modify(($newDate->format('w') === '0') ? 'now' : 'sunday this week');
            break;
    }
 
    return $newDate;
}

function get_configuration($setting) {
   
    $session = session();
    $user_session = $session->get('user_logged_in');
    
    if(!isset($user_session['company_id']))
        return '';

    $configurationModel = new \App\Models\ConfigurationModel();

    $result = $configurationModel->where(['company_id' => $user_session['company_id'], 'setting' => $setting])->get()->getRow();
    
    if(!is_object($result))
        return '';
    else
        return $result->value;
}

function get_fibonacci($n) {
    $f0 = 0;
    $f1 = 1;
    $fn = 1;
 
    if ($n < 0) {
        return - 1;
    } else if ($n == 0 || $n == 1) {
        return $n;
    } else {
        for($i = 2; $i < $n; $i ++) {
            $f0 = $f1;
            $f1 = $fn;
            $fn = $f0 + $f1;
        }
    }
    return $fn;
}

function can_retry_job($last_retry_count, $last_retry_time) {

    if($last_retry_time == "0000-00-00 00:00:00")
        return TRUE;

    $next_retry_count = $last_retry_count + 1;
    $minutes = get_fibonacci($next_retry_count);

    $next_retry_time = strtotime("+" . $minutes . " minutes", strtotime($last_retry_time));

    if(time() >= $next_retry_time )
        return TRUE;
    else
        return FALSE;
}

function set_alert($type, $message, $keep=FALSE)
{
    $session = session();
    $session->setFlashdata('alert-' . $type, $message);
   // if($keep)
   //     $session->keepFlashdata('alert-' . $type);

}

function create_paycode($invoice_id) {
    $prefix = "SEP";
    if(strlen($invoice_id) < 8) {
        $zero_add = 8 - strlen($invoice_id);
        $pay_code = $prefix;
        for($i = 0; $i < $zero_add;$i++) {
            $pay_code = $pay_code . '0';
        }

        $pay_code = $pay_code . $invoice_id;
            
        
    } else {
        $pay_code = $prefix . $invoice_id;

    }
    return $pay_code;
}

function get_month_by_billing_cycle($billing_cycle) {
    //enum('free','monthly','quarterly','semi-annually','annually','biennially','triennially')
    $b =  ['free' => 1,'monthly' => 1,'quarterly' => 3,'semi-annually' => 6,'annually' => 12,'biennially' =>24,'triennially' => 36];
    if(isset ($b[$billing_cycle]))
        return $b[$billing_cycle];
    else
        return FALSE;
}

function get_strtotime_by_billing_cycle($billing_cycle) {
    $b =  ['free' => '','monthly' => '+1 month','quarterly' => '+3 months','semi-annually' => '+6 months','annually' => '+1 year','biennially' =>'+2 years','triennially' => '+3 years'];
    if(isset ($b[$billing_cycle]))
        return $b[$billing_cycle];
    else
        return FALSE;
}

function get_text_by_billing_cycle($billing_cycle) {
    $b =  ['free' => "Miễn phí",'monthly' => 'Theo tháng','quarterly' => "Theo quý",'semi-annually' => "Sáu tháng",'annually' => "Theo năm",'biennially' =>"Hai năm",'triennially' => "Ba năm"];
    if(isset ($b[$billing_cycle]))
        return $b[$billing_cycle];
    else
        return FALSE;
}

function get_promotion_text_by_billing_cycle($billingCycle) {
    $billingCycles = [
        'free' => 'Miễn phí',
        'monthly' => '1 tháng',
        'quarterly' => '3 tháng',
        'semi-annually' => '6 tháng',
        'annually' => '1 năm',
        'biennially' => '2 năm',
        'triennially' => '3 năm',
    ];

    return isset($billingCycles[$billingCycle]) ? $billingCycles[$billingCycle] : null;
}

function add_system_log($data) {
    if(!is_array($data))
        return FALSE;

    $systemLogModel = new \App\Models\SystemLogModel();

    $result = $systemLogModel->insert($data);
    
    return $result;
}

function validHTML($html,$checkOrder=true) {
    preg_match_all( '#<([a-z]+)>#i' , $html, $start, PREG_OFFSET_CAPTURE );
    preg_match_all( '#<\/([a-z]+)>#i' , $html, $end, PREG_OFFSET_CAPTURE );
    $start = $start[1];
    $end = $end[1];

    if (count($start) != count($end) )
        return 'Kiểm tra lại tag HTML';

    if ($checkOrder) {
        $is = 0;
        foreach($end as $v){
            if ($v[0] != $start[$is][0] || $v[1] < $start[$is][1] )
                return 'End tag ['.$v[0].'] not opened';

            $is++;
        }
    }

    return true;
}

function get_bank_sms_info($brand_name=FALSE) {
    $results = [
        'Vietcombank' => [
            'personal' => [
                'stable' => '<span class="text-success">Rất cao</span>',
                'speed' => '<span class="text-success">Nhanh</span>',
                'fee' => '0-15 giao dịch: 10,000đ<br>Trên 20 giao dịch: 10,000đ + 700đ x giao dịch phát sinh',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-success">Rất cao</span>',
                'speed' => '<span class="text-success">Nhanh</span>',
                'fee' => '55.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'VPBank' => [
            'personal' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '0-15 giao dịch: 10,000đ <br>
                16-30 giao dịch: 20,000đ <br>
                31- 50 giao dịch: 30,000đ <br>
                51 - 100 giao dịch: 50,000đ <br>
                Trên 100 giao dịch: 70,000đ<br><em>Chỉ báo SMS khi GD 100.000đ trở lên.</em>',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '22.000đ<br><em>Báo SMS khi GD 1đ trở lên.</em>',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'ACB' => [
            'personal' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => '<span class="text-success">Nhanh</span>',
                'fee' => '- Từ 20 tin nhắn trở lại: 15,000đ<br>
                - Trên 20 tin nhắn: 15,000đ + 700đ x số lượng tin nhắn vượt<br><em>Chỉ báo SMS khi GD 50.000đ trở lên.</em>',
                'how_to' => '<span class="text-success">Online qua App/Web</span>',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => '<span class="text-success">Nhanh</span>',
                'fee' => '- Từ 20 tin nhắn trở lại: 15,000đ<br>
                - Trên 20 tin nhắn: 15,000đ + 700đ x số lượng tin nhắn vượt<br><em>Chỉ báo SMS khi GD 50.000đ trở lên.</em>',
                'how_to' => '<span class="text-success">Online qua App/Web</span>',
                'how_open_account' => 'Tại điểm giao dịch'

            ],
            'how_reg_sms' => '<p>Đăng ký online qua app <a href="https://acb.com.vn/ngan-hang-so/3-buoc-kham-pha-acb">ACB ONE</a> theo hướng dẫn sau:</p> <a href="'.base_url('uploads/bdsd/acb-sms.png').'" target="_blank"><img src="'.base_url('uploads/bdsd/acb-sms.png').'" class="img-fluid"></a>  <p> Quý khách lưu ý: <ul class="lh-lg">
            <li>Đăng ký số điện thoại của SePay với ngân hàng để nhận SMS biến động số dư. Cho tài khoản mà bạn đã tích hợp tại SePay.</li>
            <li>Đăng ký số điện thoại của SePay làm số điện thoại thứ hai. Số đầu tiên của bạn vẫn giữ (nếu có).</li>
            <li>Chỉ đăng ký nhận SMS biến động số dư, không đăng ký bất kỳ dịch vụ nào khác. Đặt biệt là không đăng ký nhận OTP</li>
          </ul></p><p>Hotline hỗ trợ của SePay: <a href="tel:***********">02873.059.589</a>. <br>Hotline hỗ trợ ngân hàng ACB: <a href="tel:**********">**********</a>.</p>'

        ],
        'Sacombank' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '0-30 giao dịch: 15.000đ<br>
                Trên 30 giao dịch: 16,500 + (500đ x số giao dịch vượt) 
                <br>(Tối đa 550,000đ/Tháng)',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '0-30 giao dịch: 15.000đ<br>
                Trên 30 giao dịch: 16,500 + (500đ x số giao dịch vượt) 
                <br>(Tối đa 550,000đ/Tháng)',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'HDBank' => [
            'personal' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '0-10 giao dịch: 9.000đ<br>
                11-20 giao dịch: 18.000đ<br>
                21-30 giao dịch: 27.000đ<br>
                Trên 30 giao dịch: 27,000đ + (900đ x số giao dịch vượt) (Chưa VAT)',
                'how_to' => '<span class="text-success">Online qua App/Web</span>',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '0-40 giao dịch: 30.000đ<br>
                Trên 40 giao dịch: 30,000 + (880đ x số giao dịch vượt) (Chưa VAT)',
                'how_to' => '<span class="text-success">Online qua App/Web</span>',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'VietinBank' => [
            'personal' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '- Từ 0-14 giao dịch: 11.000đ<br>
                - Trên 14 giao dịch: 11,000 + (880đ x số giao dịch vượt) <br><em>Chỉ báo SMS khi GD 50.000đ trở lên.</em>',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '- Gói 18.000đ: Chỉ báo khi giao dịch 50.000đ trở lên<br>
                - Gói 50.000đ: Báo khi giao dịch 1đ trở lên',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'Techcombank' => [
            'personal' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '- Từ 0-14 giao dịch: 12.000đ<br>
                - Từ 15-30 giao dịch: 18.000đ<br>
                - Từ 31-60 giao dịch: 40.000đ<br>
                - Trên 60 giao dịch: 75.000đ<br>',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '- 55.000đ: Không giới hạn giao dịch',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'BIDV' => [
            'personal' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '11.000đ: Chỉ báo SMS nếu giao dịch từ 30.000đ trở đi',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '55.000đ: Báo SMS nếu giao dịch 1đ trở đi.',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'MSB' => [
            'personal' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '- Gói 10.000đ: Chỉ báo SMS nếu giao dịch từ 500.000đ trở đi.<br>- Gói 15.000đ: Chỉ báo SMS nếu giao dịch từ 50.000đ trở đi.',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => 'Trung bình',
                'fee' => '20.000đ. Báo SMS nếu giao dịch từ 1đ.',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'ShinhanBank' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '10.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '10.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'TPBank' => [
            'personal' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '- Gói 11.000đ: Chỉ báo với giao dịch 500.000đ trở đi<br>- Gói 22.000đ: Báo nếu giao dịch 1đ trở đi.',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'
            ],
            'organization' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '30.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'Eximbank' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '16.500đ. <br>Trên 50 giao dịch cộng thêm phí vượt: 55.000đ',
                'how_to' => '<span class="text-success">Online qua App/Web</span>',
                'how_open_account' => '<span class="text-success">Online qua App</span>',
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '22.000đ. <br>Trên 80 giao dịch cộng thêm phí vượt: 55.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'

            ],
            'how_reg_sms' => '<h3>Với tài khoản ngân hàng cá nhân:</h3><p>Đăng ký online qua app <a href="https://go-edigi.eximbank.com.vn/">Eximbank Edigi</a> theo hướng dẫn sau:</p> <a href="'.base_url('uploads/bdsd/eximbank-bdsd.jpg').'" target="_blank"><img src="'.base_url('uploads/bdsd/eximbank-bdsd.jpg').'" class="img-fluid"></a> <h3>Với tài khoản ngân hàng doanh nghiệp:</h3> <p>Quý khách vui lòng đến phòng giao dịch gần nhất của Eximbank để thực hiện đăng ký SMS biến động số dư. Quý khách lưu ý: <ul class="lh-lg">
            <li>Đăng ký số điện thoại của SePay với ngân hàng để nhận SMS biến động số dư. Cho tài khoản mà bạn đã tích hợp tại SePay.</li>
            <li>Đăng ký số điện thoại của SePay làm số điện thoại thứ hai. Số đầu tiên của bạn vẫn giữ (nếu có).</li>
            <li>Chỉ đăng ký nhận SMS biến động số dư, không đăng ký bất kỳ dịch vụ nào khác. Đặt biệt là không đăng ký nhận OTP</li>
          </ul></p><p>Hotline hỗ trợ của SePay: <a href="tel:***********">02873.059.589</a>. <br>Hotline hỗ trợ ngân hàng Eximbank: <a href="tel:********">********</a></p>'
        ],
        'VIB' => [
            'personal' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '- Gói 16.500đ: Chỉ báo với giao dịch 500.000đ trở đi<br>- Gói 33.000đ: Báo với giao dịch 1đ trở đi.',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'

            ],
            'organization' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '- Gói 16.500đ: Chỉ báo với giao dịch 500.000đ trở đi<br>- Gói 33.000đ: Báo với giao dịch 1đ trở đi.',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'

            ],
        ],
        'Agribank' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '10.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => '<span class="text-success">Online qua App</span>'

            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '50.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'

            ],
        ],
        'PBVN' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '10.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'

            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '35.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'

            ],
    ],
     'KienLongBank' => [
            'personal' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => '<span class="text-success">Cao</span>',
                'fee' => '11.000đ. Chỉ báo với giao dịch lớn hơn 100.000đ',
                'how_to' => '<span class="text-success">Online qua App</span>',
                'how_open_account' => '<span class="text-success">Online qua App</span>'

            ],
            'organization' => [
                'stable' => '<span class="text-success">Cao</span>',
                'speed' => '<span class="text-success">Cao</span>',
                'fee' => '11.000đ. Chỉ báo với giao dịch lớn hơn 100.000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'

            ],
        ],
        'MBBank' => [
            'personal' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '22.000đ',
                'how_to' => '<span class="text-success">Online qua App/Web</span>',
                'how_open_account' => '<span class="text-success">Online qua App</span>'

            ],
            'organization' => [
                'stable' => '<span class="text-danger">Thấp</span>',
                'speed' => 'Trung bình',
                'fee' => '220.000đ/năm',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'

            ], 
            'how_reg_sms' => '<h3>Với tài khoản ngân hàng cá nhân:</h3><p>Đăng ký online qua app <a href="https://www.mbbank.com.vn/26/273/274/Chi-tiet/app-mbbank-2019-11-3-10-19-42">MB Bank</a> theo hướng dẫn sau:</p> <a href="'.base_url('uploads/bdsd/mbbank-sms.jpg').'" target="_blank"><img src="'.base_url('uploads/bdsd/mbbank-sms.jpg').'" class="img-fluid"></a> <h3>Với tài khoản ngân hàng doanh nghiệp:</h3> <p>Quý khách vui lòng đến phòng giao dịch gần nhất của MB Bank để thực hiện đăng ký SMS biến động số dư. Quý khách lưu ý: <ul class="lh-lg">
            <li>Đăng ký số điện thoại của SePay với ngân hàng để nhận SMS biến động số dư. Cho tài khoản mà bạn đã tích hợp tại SePay.</li>
            <li>Đăng ký số điện thoại của SePay làm số điện thoại thứ hai. Số đầu tiên của bạn vẫn giữ (nếu có).</li>
            <li>Chỉ đăng ký nhận SMS biến động số dư, không đăng ký bất kỳ dịch vụ nào khác. Đặt biệt là không đăng ký nhận OTP</li>
          </ul></p><p>Hotline hỗ trợ của SePay: <a href="tel:***********">02873.059.589</a>. <br>Hotline hỗ trợ ngân hàng MB Bank cá nhân: <a href="tel:**********">**********</a>. <br>Hotline hỗ trợ ngân hàng MB Bank tổ chức: <a href="tel:********">********</a></p>'

        ],
        'NHQUOCDAN' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => ' 33,000đ/tháng/sđt. Chỉ báo với giao dịch lớn hơn 20,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '33,000đ/tháng/sđt. Chỉ báo với giao dịch lớn hơn 20,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'WOORIVN' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '10.000đ/tháng/sđt',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '10.000đ/tháng/sđt',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'VietABank' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '1 - 10 giao dịch: 11,000đ/tháng<br>10 - 50  giao dịch: 33,000đ/tháng<br>>= 51 giao dịch: 55,000đ/tháng<br>Chỉ báo với giao dịch lớn hơn 100,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '1 - 10 giao dịch: 11,000đ/tháng<br>10 - 50  giao dịch: 33,000đ/tháng<br>>= 51 giao dịch: 55,000đ/tháng<br>Chỉ báo với giao dịch lớn hơn 100,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'VIETBANK' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '11.000đ/tháng/sđt chính chủ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '11.000đ/tháng/sđt chính chủ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'SHB' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '1 - 15 giao dịch: 15.000đ/tháng/sđt<br>>= 16 giao dịch: 750đ/giao dịch',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '1 - 15 giao dịch: 15.000đ/tháng/sđt<br>>= 16 giao dịch: 750đ/giao dịch',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'NamABank' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '1 - 15 giao dịch: 12.000đ/tháng/sđt<br>>= 16 giao dịch: 695đ/giao dịch<br>Chỉ báo với giao dịch lớn hơn 100,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '1 - 15 giao dịch: 12.000đ/tháng/sđt<br>>= 16 giao dịch: 695đ/giao dịch<br>Chỉ báo với giao dịch lớn hơn 100,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
        'PGBank' => [
            'personal' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '77.000đ/tháng/sđt. Chỉ báo với giao dịch lớn hơn 100,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
            'organization' => [
                'stable' => 'Trung bình',
                'speed' => 'Trung bình',
                'fee' => '77.000đ/tháng/sđt. Chỉ báo với giao dịch lớn hơn 100,000đ',
                'how_to' => 'Tại điểm giao dịch',
                'how_open_account' => 'Tại điểm giao dịch'
            ],
        ],
    ];

    if($brand_name) {
        if(isset($results[$brand_name]))
            return $results[$brand_name];
        else
            return FALSE; 
    } else {
        return $results;
    }
   
    
}

function is_admin() {
    return service('request')->getIPAddress() === '**************';
}

function is_retry_hook_with_conditions($hook, $result) {
    $retry = false;

    if (!is_null($hook->retry_conditions)) {
        try {
            $retryConditions = json_decode($hook->retry_conditions);

            if ($retryConditions->non_2xx_status_code == 1 
                && ($result['response_status_code'] < 200 || $result['response_status_code'] > 299)) {
                $retry = true;
            }

        } catch (\Exception $e) {
            log_message('error', 'Retry condition parsing failed: ' . $hook->retry_conditions);
        }
    }

    return $retry;
}


function xss_clean($str) {
    $str = preg_replace('/[^a-zA-Z0-9-.,\/àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ\s]/', '', $str);
    return $str;
} 

function notify_sim_config($type, $user, $company, $planName, $billingCycle) {
    $simCount = model(\App\Models\SimCompanyModel::class)->where(['company_id' => $company->id])->countAllResults();

    if ($type == 'assign' && $simCount > 0) return;
    if ($type == 'unassign' && $simCount == 0) return;

    $simActionText = $type === 'assign' ? '➕ gắn' : '✖️ gỡ'; 
    $config = config(\App\Config\Billing::class);
    $message = '
------------------------------
‼️ Khách hàng đổi gói dịch vụ cần ' . $simActionText . ' SIM:

📦 Gói dịch vụ đã đổi: ' . $planName . ' (' . $billingCycle . ')

#️⃣ Thông tin KH:
    - Họ tên: ' . $user->lastname . ' ' . $user->firstname . '
    - Email: ' . $user->email . '
    - Tên công ty/tổ chức: ' . $company->full_name . ' (' .  $company->short_name . ')

⏰ Thực hiện lúc: ' . date("Y-m-d H:i:s") . '
------------------------------
                            ';
    
    $telegramQueueModel = model(\App\Models\NotificationTelegramQueueModel::class);
    $telegramQueueModel->insert([
        'chat_id' => $config->telegramChatId,
        'status' => 'Pending',
        'message' => $message
    ]);
}

function calc_subscription_remaining_days($end_date) {
    $remainingDays = date_diff(date_create(date('Y-m-d')), date_create($end_date))->format('%R%a');

    return $remainingDays > 0 ? str_replace('+', '', $remainingDays) : 0;
}

function calc_subscription_exchange_remaining_money($begin_date, $end_date, $total) {
    $days = date_diff(date_create($end_date), date_create($begin_date))->format('%a');
    return round($total / $days * calc_subscription_remaining_days($end_date), 0);
}

function calc_subscription_exchange_remaining_money_by_transaction_count($subscriptionDetails) {
    if ($subscriptionDetails->status != 'Active') return 0;
    
    $result = model(CounterModel::class)->select("sum(transaction) as `sum_transaction`")->where([
        'company_id' => $subscriptionDetails->company_id, 
        'date >=' => $subscriptionDetails->begin_date, 
        'date <=' => $subscriptionDetails->end_date
    ])->get()->getRow();
    
    $usedTransactionCount = $result->sum_transaction ?? 0;
    
    $maxTransactionCount = $subscriptionDetails->monthly_transaction_limit * get_month_by_billing_cycle($subscriptionDetails->billing_cycle);
    
    try {
        $referalTransactionCount = get_bonus_transactions($subscriptionDetails->company_id);
        $bonusTransactionCount = model(BankBonusModel::class)->where(['company_id' => $subscriptionDetails->company_id])->selectSum('bank_bonus')->get()->getRow()->bank_bonus ?? 0;
        $bonusTransactionCount = min($bonusTransactionCount, config(\Config\BankBonus::class)->maxBankBonus) * get_month_by_billing_cycle($subscriptionDetails->billing_cycle);
    } catch (\Throwable $e) {
        $referalTransactionCount = 0;
        $bonusTransactionCount = 0;
    }
    
    $maxTransactionCount = $maxTransactionCount - $bonusTransactionCount - $referalTransactionCount;
    
    if ($usedTransactionCount >= $maxTransactionCount) return 0;
    
    $rate = 1 - $usedTransactionCount / $maxTransactionCount;
    
    return round($subscriptionDetails->recurring_payment * $rate, 0);
}

function is_upgrade_subscription($current_subscription, $new_product) {
    return $current_subscription->billing_cycle == 'monthly' && $current_subscription->recurring_payment < $new_product->price_monthly
        || $current_subscription->billing_cycle == 'annually' && $current_subscription->recurring_payment < $new_product->price_annually * 12
        || $current_subscription->plan_id == $new_product->id && $current_subscription->billing_cycle === 'monthly';
}

function get_ticket_status_badge($status) {
    if($status == "Open")
        $badge = "<span class='badge rounded-pill bg-primary'>Đang mở</span>";  
    else if($status == "Answered")
        $badge = "<span class='badge rounded-pill bg-success'>Đã trả lời</span>";  
    else if($status == "InProgress")
        $badge = "<span class='badge rounded-pill bg-danger'>Đang xử lý</span>";  
    else if($status == "ClientReply")
        $badge = "<span class='badge rounded-pill bg-info'>Khách hàng trả lời</span>";  
    else if($status == "Closed")
        $badge = "<span class='badge rounded-pill bg-secondary'>Đã đóng</span>";  
    else
        $badge = esc($status); 
    return $badge; 
}

function is_tax_code($identification_number)
{
    return strlen($identification_number) === 10 || strlen($identification_number) === 13;
}

function uuid()
{
    $db = db_connect();
    
    return $db->query("SELECT UUID() as uuid")->getRow()->uuid;
}

function mobile_feature_visibility()
{
    $config = config(\Config\Mobile::class);

    return $config->visibility ?? false;
}

function theme_view($view, $data = [])
{
    $channelPartner = session()->get('channel_partner');

    if ($channelPartner) {
        $data['channel_partner'] = $channelPartner;
    }

    $theme = $channelPartner ? $channelPartner['code'] : null;
    $session = session();

    if (!$theme && $session->get('shop_billing')) {
        $theme = 'shop-billing';
    }
    
    $session = session();

    if (!$theme && $session->get('shop_billing')) {
        $theme = 'shop-billing';
    }
    
    $session = session();
   
    if (!$theme && $session->get('speaker_billing')) {
        $theme = 'speaker-billing';
    }

    try {
        return view(($theme ? "theme/{$theme}/" : '') . $view, $data); 
    } catch (\Exception $e) {
        return view($view, $data); 
    }
}

function slavable_model($modelClassName, $key, $connection = null)
{
    $optimizationConfig = config(\Config\Optimization::class);

    $availableModels = array_keys($optimizationConfig->slave['models']);

    if (in_array($modelClassName, $availableModels) 
        && isset($optimizationConfig->slave['models'][$modelClassName][$key]) 
        && $optimizationConfig->slave['models'][$modelClassName][$key] === true) {
        $dbConfig = config(\Config\Database::class);

        if (is_null($connection)) {
            $db = db_connect($dbConfig->read);
        } else {
            if (property_exists($dbConfig, $connection)) {
                $db = db_connect($dbConfig->{$connection});
            } else {
                $db = db_connect($dbConfig->read);
            }
        }

        if ($optimizationConfig->slave['debug']) {
            log_message('error', "[Optimization]: Query was routed into mysql slave {$modelClassName} - {$key} - {$connection} " . json_encode(debug_backtrace()[0]));
        }

        return model($modelClassName, false, $db);
    }

    return model($modelClassName);
}

function is_tomorrow($date1, $date2) {
    $date1 = new DateTime($date1);
    $date2 = new DateTime($date2);

    // Add one day to the first date
    $date1->modify('+1 day');

    // Compare if the modified date1 equals date2
    return $date1->format('Y-m-d') === $date2->format('Y-m-d');
}

function is_channel_partner(){
    $session = session();
    $isChannelPartner = $session->get('channel_partner');

    if(!$isChannelPartner){
        throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
    }
}

function allowed_tags($input) {
    $allowedTags = '<a><b><strong><i><em><s><strike><del><u><ins><span><tg-spoiler>';
    
    return strip_tags($input, $allowedTags);
}

function generate_bank_features($json) {
    $features = [
        'va' => ['icon' => 'bi bi-wallet2', 'label' => 'Tài khoản ảo (VA)'],
        'amount-in' => ['icon' => 'bi bi-arrow-left-right', 'label' => 'Đồng bộ tiền vào'],
        'amount-out' => ['icon' => 'bi bi-arrow-left-right', 'label' => 'Đồng bộ tiền ra'],
        'balance' => ['icon' => 'bi bi-arrow-repeat', 'label' => 'Đồng bộ số dư'],
    ];

    $bankFeatures = json_decode($json ?: '', true);
    $html = '';

    if (! empty($bankFeatures)) {
        $html = '<ul class="list-unstyled mb-0">';
        $displayed = [];
        if (in_array('amount-in', $bankFeatures) && in_array('amount-out', $bankFeatures)) {
            $html .= '<li><i class="bi bi-arrow-left-right me-1"></i> Đồng bộ tiền vào, tiền ra</li>';
            $displayed[] = 'amount-in';
            $displayed[] = 'amount-out';
        }
        foreach ($bankFeatures as $featureKey) {
            if (in_array($featureKey, $displayed)) {
                continue;
            }
            if (isset($features[$featureKey])) {
                $html .= '<li><i class="' . $features[$featureKey]['icon'] . ' me-1"></i> ' . $features[$featureKey]['label'] . '</li>';
            }
        }
        $html .= '</ul>';
    } else {
        $html .= '—';
    }

    return $html;
}

function get_bonus_transactions($companyId): ?int
{
    $referralCode = model(\App\Models\ReferralCodeModel::class)
        ->where('company_id', $companyId)
        ->where('is_active', true)
        ->first();

    if (! $referralCode) {
        return null;
    }

    return $referralCode->total_bonus_received;
}

function str_mask(string $string, string $character, int $index, int $length = null): string {
    if ($character === '') {
        return $string;
    }

    $segment = mb_substr($string, $index, $length);

    if ($segment === '') {
        return $string;
    }

    $start = mb_substr($string, 0, mb_strpos($string, $segment, 0));
    $end = mb_substr($string, mb_strpos($string, $segment, 0) + mb_strlen($segment));

    return $start . str_repeat(mb_substr($character, 0, 1), mb_strlen($segment)).$end;
}

function in_testing(): bool {
    return ENVIRONMENT === 'testing';
}

function bank_account_supported_balance($bankAccountId)
{
    $bankAccount = model(\App\Models\BankAccountModel::class)
        ->select('tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank.brand_name')
        ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
        ->find($bankAccountId);

    return in_array($bankAccount->brand_name, ['VietinBank', 'TPBank']) || $bankAccount->bank_sms || $bankAccount->bank_sms_connected;   
}

function bank_account_supported_amount_out($bankAccountId)
{
    $bankAccount = model(\App\Models\BankAccountModel::class)
        ->select('tb_autopay_bank_account.bank_sms, tb_autopay_bank_account.bank_sms_connected, tb_autopay_bank.brand_name')
        ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
        ->find($bankAccountId);

    return in_array($bankAccount->brand_name, ['VietinBank', 'TPBank', 'ACB', 'MBBank']) || $bankAccount->bank_sms || $bankAccount->bank_sms_connected;   
}

function bank_account_settings($bankAccountId)
{
    return \App\Libraries\BankAccountSettingService::forBankAccount($bankAccountId);
}

function can_show_bank_account_settings($bankId, $sms = false)
{
    if ($sms) {
        $bankConfig = config(\Config\Sms::class);
    } else {
        switch ($bankId) {
            case 2:
                $bankConfig = config(\Config\Vpbank::class);
                break;
            case 3:
                $bankConfig = config(\Config\Acb::class);
                break;
            case 6:
                $bankConfig = config(\Config\Vietinbank::class);
                break;
            case 8:
                $bankConfig = config(\Config\Mbb::class);
                break;
            case 9:
                $bankConfig = config(\Config\Bidv::class);
                break;
            case 10:
                $bankConfig = config(\Config\Msb::class);
                break;
            case 12:
                $bankConfig = config(\Config\Tpbank::class);
                break;
            case 17:
                $bankConfig = config(\Config\Klb::class);
                break;
            case 18:
                $bankConfig = config(\Config\Ocb::class);
                break;
            default:
                $bankConfig = false;
                break;
        }
    }
    
    return $bankConfig->enableFilterTransaction ?? false;
}

function check_oauth2_allowed_ip()
{
    $config = config(\Config\OAuth2::class);

    if (
        ! empty($config->allowedIps)
        && ! in_array(service('request')->getIPAddress(), $config->allowedIps)
    ) {
        return false;
    }

    return true;
}

if (! function_exists('oauth2')) {
    function oauth2()
    {
        return new \App\Libraries\OAuthServer\OAuth2();
    }
}

if (! function_exists('auth')) {
    function auth()
    {
        return new \App\Libraries\Auth(
            service('session'),
            service('request'),
            model(\App\Models\UserModel::class),
            model(\App\Models\CompanyUserModel::class)
        );
    }
}


function phone_number_regex()
{
    return '/^0\d+$/';
}
function speaker_billing_feature()
{
    return new \App\Features\SpeakerBilling\SpeakerBillingFeature;
}

function component(string $name, array $data = [])
{
    return view($name, $data, ['saveData' => true]);
}

function component_style(string $name)
{
    return view($name, ['style' => true], ['saveData' => false]);
}

function component_script(string $name)
{
    return view($name, ['script' => true], ['saveData' => false]);
}

function is_bank_box_support(?bool $value = null): bool
{
    $config = config(\Config\BankBox::class);

    if (!is_null($value)) {
        $config->enabled = $value;
    }

    return property_exists($config, 'enabled') ? is_admin() || $config->enabled : false;
}

function is_speaker_billing_subscription(): bool
{
    try {
        return service('speakerBillingFeature') && service('speakerBillingFeature')->companyContext()->isSubscribedSpeakerBillingProduct();
    } catch (\Exception $e) {
        return false;
    }
}

function is_shop_billing_subscription(): bool
{
    try {
        $session = service('session');
        
        return $session->get('shop_billing') ?? false;
    } catch (\Exception $e) {
        return false;
    }
}

function format_currency($value)
{
    if (is_numeric($value)) {
        return number_format($value, 0, ',', '.') . ' đ';
    } else {
        return $value;
    }
}

function is_shop_billing()
{
    $session = session();

    return $session->get('shop_billing');
}

function get_config($config, $key, $fallback = null) {
    if (!$config) {
        return $fallback;
    }
    
    $resolveCalback = function($value) use ($fallback) {
        if (is_callable($fallback)) {
            return $fallback($value);
        }
        
        return $value;
    };

    return $resolveCalback(property_exists($config, $key) ? $config->$key : null);
}

function is_bank_supported_by_promotion($bank_id = null, $company_id = null)
{
    $subscription_details = model(\App\Models\CompanySubscriptionModel::class)
        ->select('plan_id')
        ->where('company_id', $company_id)
        ->first();

    if ($subscription_details) {
        $productPromotion = model(App\Models\ProductPromotionModel::class)
            ->select('tb_autopay_product_promotions.*')
            ->join('tb_autopay_company_promotion_history', 'tb_autopay_company_promotion_history.promotion_id = tb_autopay_product_promotions.id')
            ->where('tb_autopay_product_promotions.product_id', $subscription_details->plan_id)
            ->where('tb_autopay_company_promotion_history.company_id', $company_id)
            ->where('tb_autopay_company_promotion_history.product_id', $subscription_details->plan_id)
            ->first();

        if (! $productPromotion) {
            return;
        }

        if (! $bank_id) {
            throw new Exception('Product promotion detected');
        }

        if ($bank_id != $productPromotion->bank_id) {
            show_404();
        }
    }
}

function remove_accents_with_intl(string $string): string
{
    $transliterator = \Transliterator::create('NFD; [:Nonspacing Mark:] Remove; NFC');
    $string = $transliterator->transliterate($string);
    $string = str_replace('đ', 'd', $string);
    $string = str_replace('Đ', 'D', $string);

    return $string;
}

function get_nested($data, string $path, $default = null)
{
    $keys = explode('.', $path);

    foreach ($keys as $key) {
        if (is_object($data)) {
            $data = $data->$key ?? null;
        } elseif (is_array($data)) {
            $data = $data[$key] ?? null;
        } else {
            return $default;
        }
    }

    return $data ?? $default;
}

function can_switch_plan_from_speaker_billing(): bool
{
    $companyId = session()->get('user_logged_in')['company_id'] ?? null;

    if (! $companyId) {
        return false;
    }

    $source = model(\App\Models\TrackingModel::class)
        ->select('utm_source')
        ->where('company_id', $companyId)
        ->first();

    if ($source && $source->utm_source === 'loa.sepay.vn') {
        return false;
    }

    $outputDevices = model(\App\Models\OutputDeviceModel::class)
        ->where('company_id', $companyId)
        ->countAllResults();

    return $outputDevices === 0;
}

function can_switch_speaker_plan_from_free_plan(): bool
{
    $companyId = session()->get('user_logged_in')['company_id'] ?? null;

    if (! $companyId) {
        return false;
    }

    $companySubscription = model(\App\Models\CompanySubscriptionModel::class)
        ->where('company_id', $companyId)
        ->where('billing_cycle', 'free')
        ->first();

    if (! $companySubscription) {
        return false;
    }

    $bankAccounts = model(\App\Models\BankAccountModel::class)
        ->where('company_id', $companyId)
        ->countAllResults();

    if ($bankAccounts > 0) {
        return false;
    }

    return ! is_speaker_billing_subscription();
}

function get_zns_notification($field) {
    $znsNotificationModel = model(\App\Models\ZNSNotificationModel::class);
    $company_id = session()->get('user_logged_in')['company_id'];
    $result = $znsNotificationModel->getZNSNotification($company_id);
    
    if(is_object($result)) {
        return $result->$field;
    }
    return '';
}

function pg_billing_enabled(): bool
{
    $billingFeature = new \App\Features\BillingFeature;
    
    return $billingFeature->pgEnabled;
}
