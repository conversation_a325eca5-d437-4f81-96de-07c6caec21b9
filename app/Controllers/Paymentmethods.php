<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PgMerchantModel;
use App\Models\PgPaymentMethodModel;
use App\Models\PgProfileModel;
use App\Models\BankAccountModel;
use App\Models\OnboardingSubmissionModel;
use App\Models\PgNapasVietqrProfileModel;
use App\Models\BankSubAccountModel;
use App\Models\PgBranchProfileModel;
use App\Models\PgBranchModel;
use App\Enums\PgPaymentMethod;
use App\Enums\PgProfileType;
use App\Models\PgBranchTerminalModel;
use App\Models\BidvEnterpriseAccountModel;

class Paymentmethods extends BaseController
{
    public function index()
    {
        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        $data = [
            'page_title' => 'Phương thức thanh toán',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'merchant' => $merchant,
            'payment_methods' => $this->getPaymentMethods($merchant->id ?? null),
            'bank_accounts' => $this->getBankAccounts(),
            'onboarding_status' => $this->getOnboardingStatus(),
            'features' => $this->getPaymentFeatures(),
            'payment_method_exists' => $this->getPaymentMethodExistence($merchant->id ?? null)
        ];

        return view('payment-methods/index', $data);
    }

    public function banktransfer()
    {
        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return redirect()->to('paymentmethods')->with('error', 'Merchant chưa được tạo');
        }

        $paymentMethod = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => PgPaymentMethod::BANK_TRANSFER
            ])
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $paymentMethod) {
            return redirect()->to('paymentmethods')->with('error', 'Phương thức thanh toán chuyển khoản ngân hàng chưa được tạo. Vui lòng kích hoạt trước.');
        }

        $data = [
            'page_title' => 'Chuyển khoản ngân hàng',
            'user_details' => $this->user_details,
            'company_details' => $this->company_details,
            'merchant' => $merchant,
            'payment_method' => $paymentMethod,
            'bankProfiles' => $this->getBankProfiles($merchant->id, $paymentMethod->id ?? null),
            'available_bank_accounts' => $this->getAvailableBankAccounts()
        ];

        return view('payment-methods/bank-transfer', $data);
    }

    public function setDefaultBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'profile_id' => 'required|numeric',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo',
            ]);
        }

        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $data['profile_id'],
                'pg_merchant_id' => $merchant->id,
                'active' => 1,
                'company_id' => $this->company_details->id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->first();

        if (! $profile) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản thụ hưởng không tồn tại',
            ]);
        }

        model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'pg_payment_method_id' => $profile->pg_payment_method_id,
                'company_id' => $this->company_details->id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->set(['default' => 0])
            ->update();

        model(PgProfileModel::class)->update($profile->id, ['default' => true]);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã cập nhật tài khoản thụ hưởng mặc định thành công',
        ]);
    }

    public function addBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->jsonResponse(false, 'Invalid request');
        }

        $data = $this->request->getPost();
        if (! $this->validate([
            'bank_account_id' => 'required|numeric',
            'is_default' => 'permit_empty|in_list[0,1]',
            'use_dynamic_va' => 'permit_empty|in_list[0,1]'
        ])) {
            return $this->jsonResponse(false, null, $this->validator->getErrors());
        }

        if (! $merchant = $this->getMerchant()) return $this->jsonResponse(false, 'Merchant chưa được tạo');

        if (! $paymentMethod = $this->getBankTransferPaymentMethod($merchant->id)) {
            return $this->jsonResponse(false, 'Phương thức thanh toán chuyển khoản chưa được kích hoạt');
        }

        if (! $bankAccount = $this->getValidBankAccount($data['bank_account_id'])) {
            return $this->jsonResponse(false, 'Tài khoản ngân hàng không hợp lệ');
        }

        $supportsDynamicVA = $this->isBidvEnterprise($bankAccount);
        $useDynamicVA = ($data['use_dynamic_va'] ?? '0') === '1';

        $validation = $this->validateVARequirements($bankAccount->brand_name, $useDynamicVA, $supportsDynamicVA, $data['bank_sub_account_id'] ?? null);

        if (! $validation['valid']) {
            return $this->jsonResponse(false, $validation['message']);
        }

        if ($this->profileExists($merchant->id, $paymentMethod->id, $data['bank_account_id'])) {
            return $this->jsonResponse(false, 'Tài khoản ngân hàng này đã được thêm vào tài khoản thụ hưởng');
        }

        $isDefault = ($data['is_default'] ?? '0') === '1';

        if ($isDefault) {
            $this->clearDefaultProfiles($merchant->id, $paymentMethod->id);
        }

        $profileId = $this->createProfile($merchant->id, $paymentMethod->id, $data['bank_account_id'], $isDefault);
        $this->createBranchProfile($merchant->id, $paymentMethod->id, $profileId, $data['bank_account_id'], $useDynamicVA, $data['bank_sub_account_id'] ?? null, $bankAccount->brand_name);

        return $this->jsonResponse(true, 'Đã thêm tài khoản thụ hưởng thành công', ['profile_id' => $profileId]);
    }

    public function removeBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'profile_id' => 'required|numeric'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo'
            ]);
        }

        $profile = model(PgProfileModel::class)
            ->where([
                'id' => $data['profile_id'],
                'pg_merchant_id' => $merchant->id,
                'company_id' => $this->company_details->id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->first();

        if (! $profile) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tài khoản thụ hưởng không tồn tại'
            ]);
        }

        $profileCount = model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'pg_payment_method_id' => $profile->pg_payment_method_id,
                'active' => 1,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->countAllResults();

        if ($profileCount <= 1) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể xóa tài khoản thụ hưởng cuối cùng. Phải có ít nhất một tài khoản thụ hưởng.',
            ]);
        }

        if ($profile->default) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Không thể xóa tài khoản thụ hưởng mặc định. Vui lòng chọn tài khoản thụ hưởng khác làm mặc định trước khi xóa.',
            ]);
        }

        if ($profile->type === PgProfileType::NAPAS_VIETQR) {
            $napasRecord = model(PgNapasVietqrProfileModel::class)
                ->where('pg_profile_id', $profile->id)
                ->first();

            if ($napasRecord) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Không thể xóa profile NAPAS VietQR đang có dữ liệu liên kết. Vui lòng liên hệ quản trị viên.',
                ]);
            }
        }

        model(PgProfileModel::class)->delete($profile->id);

        return $this->response->setJSON([
            'status' => true,
            'message' => 'Đã xóa tài khoản thụ hưởng thành công'
        ]);
    }

    public function toggleFeature()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Yêu cầu không hợp lệ',
            ]);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'feature_id' => 'required',
            'action' => 'required|in_list[enable,disable]',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $validFeatures = ['napas_qr', 'card'];

        if (! in_array($data['feature_id'], $validFeatures)) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Tính năng không hợp lệ'
            ]);
        }

        if ($data['action'] === 'enable') {
            model(OnboardingSubmissionModel::class)->createOnboardingRecord($this->company_details->id, $data['feature_id']);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đang chuyển hướng đến quá trình đăng ký...',
                'redirect' => base_url("onboarding?feature={$data['feature_id']}&step=1")
            ]);
        } else if ($data['action'] === 'disable') {
            model(OnboardingSubmissionModel::class)->updateStatus($this->company_details->id, $data['feature_id'], 'rejected');

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã tạm ngưng tính năng thành công'
            ]);
        }

        return $this->response->setJSON([
            'status' => false,
            'message' => 'Hành động không hợp lệ'
        ]);
    }

    public function togglePaymentMethod()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'payment_method' => 'required|in_list[' . implode(',', [PgPaymentMethod::BANK_TRANSFER, PgPaymentMethod::CARD]) . ']',
            'action' => 'required|in_list[enable,disable]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Merchant chưa được tạo',
            ]);
        }

        $existing = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => $data['payment_method'],
            ])
            ->first();

        if (! $existing) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Phương thức thanh toán không tồn tại'
            ]);
        }

        if ($data['action'] === 'enable') {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 1]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã kích hoạt phương thức thanh toán'
            ]);
        } else {
            model(PgPaymentMethodModel::class)->update($existing->id, ['active' => 0]);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã tạm ngưng phương thức thanh toán'
            ]);
        }
    }

    private function getPaymentMethods($merchantId)
    {
        if (! $merchantId) return [];

        return model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $merchantId)
            ->findAll();
    }

    private function getBankAccounts()
    {
        return $this->getAvailableBankAccounts();
    }

    private function getOnboardingStatus()
    {
        $methods = ['card', 'napas_qr'];
        $status = [];

        foreach ($methods as $method) {
            $submission = model(OnboardingSubmissionModel::class)
                ->getByCompanyAndMethod($this->company_details->id, $method);
            $status[$method] = $submission ? $submission->status : 'inactive';
        }

        return $status;
    }

    private function getPaymentFeatures()
    {
        $features = [
            [
                'id' => 'bank_qr',
                'payment_method' => 'bank_qr',
                'name' => 'Chuyển khoản ngân hàng qua QR',
                'description' => 'Nhận thanh toán trực tiếp vào tài khoản ngân hàng thông qua mã QR. Sử dụng ngay không cần đăng ký.',
                'icon' => 'bi-bank',
                'features' => ['Chuyển khoản trực tiếp vào TK ngân hàng', 'Không cần đăng ký phức tạp', 'Sử dụng ngay lập tức'],
                'is_default_enabled' => true,
            ],
            [
                'id' => 'napas_qr',
                'payment_method' => 'napas_qr',
                'name' => 'QR theo đơn hàng NAPAS',
                'description' => 'Thanh toán bằng QR code chuẩn quốc gia NAPAS theo từng đơn hàng cụ thể. Hỗ trợ tất cả ngân hàng trong nước.',
                'icon' => 'bi bi-qr-code',
                'features' => ['QR chuẩn quốc gia NAPAS', 'Theo dõi từng đơn hàng', 'Phí theo % giao dịch', 'Hỗ trợ tất cả ngân hàng nội địa']
            ],
            [
                'id' => 'card',
                'payment_method' => 'card',
                'name' => 'Thanh toán bằng thẻ',
                'description' => 'Chấp nhận thanh toán bằng thẻ tín dụng/ghi nợ quốc tế và trong nước. Được cung cấp bởi VPBank.',
                'icon' => 'bi-credit-card',
                'features' => ['Thẻ Visa, Mastercard, JCB', 'Thẻ ATM nội địa', 'Phí theo % giao dịch', 'Bảo mật 3D Secure']
            ],
        ];

        foreach ($features as &$feature) {
            if (isset($feature['is_default_enabled']) && $feature['is_default_enabled']) {
                $feature['status'] = 'available';
                $feature['onboard_completed'] = true;
                $feature['current_step'] = null;
            } else {
                $submission = model(OnboardingSubmissionModel::class)->getByCompanyAndMethod($this->company_details->id, $feature['payment_method']);
                $feature['status'] = $submission ? $submission->status : 'inactive';
                $feature['onboard_completed'] = $submission ? $submission->submitted_at !== null : false;
                $feature['current_step'] = $submission ? ($submission->current_step ?: 1) : null;
            }
        }

        return $features;
    }

    private function getBankProfiles($merchantId, $paymentMethodId)
    {
        if (! $paymentMethodId) return [];

        $commonWhere = [
            'tb_autopay_pg_profile.pg_merchant_id' => $merchantId,
            'tb_autopay_pg_profile.pg_payment_method_id' => $paymentMethodId,
            'tb_autopay_pg_profile.active' => 1,
            'tb_autopay_pg_profile.company_id' => $this->company_details->id,
        ];

        $bankAccountProfiles = model(PgProfileModel::class)
            ->select('tb_autopay_pg_profile.*, tb_autopay_bank_account.account_number, tb_autopay_bank_account.account_holder_name, tb_autopay_bank_account.id as bank_account_id, tb_autopay_bank_account.bank_api_connected, bank.brand_name, bank.logo_path, bank.icon_path')
            ->join('tb_autopay_bank_account', 'tb_autopay_bank_account.id = tb_autopay_pg_profile.bank_account_id')
            ->join('tb_autopay_bank bank', 'bank.id = tb_autopay_bank_account.bank_id')
            ->where(array_merge($commonWhere, [
                'tb_autopay_pg_profile.type' => PgProfileType::BANK_ACCOUNT,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
            ]))
            ->findAll();

        $napasProfiles = model(PgProfileModel::class)
            ->select('tb_autopay_pg_profile.*, tb_autopay_pg_napas_vietqr_profile.acquiring_account_holder_name as account_holder_name, tb_autopay_pg_napas_vietqr_profile.vac as account_number, "NAPAS VietQR" as brand_name, "" as logo_path, "" as icon_path')
            ->join('tb_autopay_pg_napas_vietqr_profile', 'tb_autopay_pg_napas_vietqr_profile.pg_profile_id = tb_autopay_pg_profile.id')
            ->where(array_merge($commonWhere, ['tb_autopay_pg_profile.type' => PgProfileType::NAPAS_VIETQR]))
            ->findAll();

        $allProfiles = array_merge($bankAccountProfiles, $napasProfiles);
        usort($allProfiles, fn($a, $b) => $a->default != $b->default ? $b->default - $a->default : $a->id - $b->id);

        return $allProfiles;
    }

    private function getAvailableBankAccounts()
    {
        return model(BankAccountModel::class)
            ->select('tb_autopay_bank_account.*, tb_autopay_bank_account.bank_api_connected, bank.brand_name, bank.logo_path, bank.icon_path')
            ->join('tb_autopay_bank bank', 'bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.company_id' => $this->company_details->id,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
            ])
            ->orderBy('tb_autopay_bank_account.id', 'DESC')
            ->findAll();
    }

    private function getPaymentMethodExistence($merchantId)
    {
        if (! $merchantId) return [];

        $paymentMethods = model(PgPaymentMethodModel::class)
            ->where('pg_merchant_id', $merchantId)
            ->where('company_id', $this->company_details->id)
            ->findAll();

        $existence = [];

        foreach ($paymentMethods as $method) {
            $existence[$method->payment_method] = true;
        }

        return $existence;
    }

    public function getBankSubAccounts()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $bankAccountId = $this->request->getGet('bank_account_id');

        if (! is_numeric($bankAccountId)) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid bank account ID']);
        }

        $bankAccount = model(BankAccountModel::class)
            ->where([
                'id' => $bankAccountId,
                'company_id' => $this->company_details->id,
                'active' => 1,
                'bank_api_connected' => 1,
            ])
            ->first();

        if (! $bankAccount) {
            return $this->response->setJSON(['status' => false, 'message' => 'Bank account not found']);
        }

        $bankSubAccounts = model(BankSubAccountModel::class)
            ->select(['id', 'sub_account', 'sub_holder_name', 'label', 'acc_type', 'active'])
            ->where([
                'bank_account_id' => $bankAccountId,
                'active' => true,
                'acc_type' => 'Real',
                'va_active' => true,
            ])
            ->findAll();

        $formattedSubAccounts = array_map(function ($subAccount) {
            return [
                'id' => (int) $subAccount->id,
                'account_number' => $subAccount->sub_account,
                'account_holder_name' => $subAccount->sub_holder_name ?: $subAccount->label,
                'label' => $subAccount->label,
                'acc_type' => $subAccount->acc_type
            ];
        }, $bankSubAccounts);

        return $this->response->setJSON([
            'status' => true,
            'data' => $formattedSubAccounts
        ]);
    }

    public function saveVAProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->jsonResponse(false, 'Invalid request');
        }

        $data = $this->request->getPost();
        if (! $this->validate([
            'profile_id' => 'required|numeric',
            'bank_sub_account_id' => 'permit_empty|numeric',
            'use_dynamic_va' => 'permit_empty|in_list[0,1]'
        ])) {
            return $this->jsonResponse(false, null, $this->validator->getErrors());
        }

        $merchant = $this->getMerchant();
        if (! $merchant) return $this->jsonResponse(false, 'Merchant chưa được tạo');

        $profile = $this->getProfile($data['profile_id'], $merchant->id);
        if (! $profile) return $this->jsonResponse(false, 'Profile không tồn tại');

        $pgBranch = $this->getPgBranch($merchant->id);
        if (! $pgBranch) return $this->jsonResponse(false, 'PG Branch chưa được tạo');

        $paymentMethod = $this->getBankTransferPaymentMethod($merchant->id);
        if (! $paymentMethod) return $this->jsonResponse(false, 'Payment method không tồn tại');

        if (!empty($data['bank_sub_account_id']) && ! $this->bankSubAccountExists($data['bank_sub_account_id'])) {
            return $this->jsonResponse(false, 'Bank sub account không tồn tại');
        }

        $isUpdate = $this->saveOrUpdateBranchProfile($profile, $merchant, $pgBranch, $paymentMethod, $data);
        $message = $isUpdate ? 'Đã cập nhật cấu hình VA thành công' : 'Đã lưu cấu hình VA thành công';

        return $this->jsonResponse(true, $message);
    }

    public function getAllVAProfiles()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $merchant = model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $merchant) {
            return $this->response->setJSON(['status' => false, 'message' => 'Merchant not found']);
        }

        $paymentMethod = model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'payment_method' => PgPaymentMethod::BANK_TRANSFER
            ])
            ->where('company_id', $this->company_details->id)
            ->first();

        if (! $paymentMethod) {
            return $this->response->setJSON(['status' => false, 'message' => 'Payment method not found']);
        }

        $pgBranch = model(PgBranchModel::class)
            ->where([
                'pg_merchant_id' => $merchant->id,
                'company_id' => $this->company_details->id
            ])
            ->first();

        if (! $pgBranch) {
            return $this->response->setJSON(['status' => false, 'message' => 'Branch not found']);
        }

        $branchProfiles = model(PgBranchProfileModel::class)
            ->select('tb_autopay_pg_branch_profile.pg_profile_id, tb_autopay_pg_branch_profile.use_dynamic_va, tb_autopay_bank_sub_account.id, tb_autopay_bank_sub_account.sub_account, tb_autopay_bank_sub_account.sub_holder_name, tb_autopay_bank_sub_account.label')
            ->join('tb_autopay_bank_sub_account', 'tb_autopay_bank_sub_account.id = tb_autopay_pg_branch_profile.bank_sub_account_id', 'left')
            ->where([
                'tb_autopay_pg_branch_profile.pg_merchant_id' => $merchant->id,
                'tb_autopay_pg_branch_profile.pg_branch_id' => $pgBranch->id,
                'tb_autopay_pg_branch_profile.pg_payment_method_id' => $paymentMethod->id,
                'tb_autopay_pg_branch_profile.company_id' => $this->company_details->id
            ])
            ->findAll();

        $vaData = [];
        foreach ($branchProfiles as $profile) {
            $vaData[$profile->pg_profile_id] = [
                'use_dynamic_va' => (bool) $profile->use_dynamic_va,
                'id' => $profile->id,
                'account_number' => $profile->sub_account,
                'account_holder_name' => $profile->sub_holder_name,
                'label' => $profile->label
            ];
        }

        return $this->response->setJSON([
            'status' => true,
            'data' => $vaData
        ]);
    }

    public function checkBidvEnterpriseSupport()
    {
        if (! $this->request->isAJAX()) {
            return $this->jsonResponse(false, 'Invalid request');
        }

        $bankAccountId = $this->request->getGet('bank_account_id');
        if (! is_numeric($bankAccountId)) {
            return $this->jsonResponse(false, 'Invalid bank account ID');
        }

        $bankAccount = $this->getValidBankAccount($bankAccountId);
        if (! $bankAccount) {
            return $this->jsonResponse(false, 'Bank account not found');
        }

        $supportsDynamicVA = $this->isBidvEnterprise($bankAccount);

        return $this->jsonResponse(true, null, [
            'supports_dynamic_va' => $supportsDynamicVA,
            'bank_name' => $bankAccount->brand_name
        ]);
    }

    private function jsonResponse($status, $message = null, $data = null)
    {
        $response = ['status' => $status];
        if ($message) $response['message'] = $message;
        if ($data) {
            if (isset($data['errors'])) {
                $response['errors'] = $data['errors'];
            } else {
                $response = array_merge($response, $data);
            }
        }
        return $this->response->setJSON($response);
    }

    private function getMerchant()
    {
        return model(PgMerchantModel::class)
            ->where('company_id', $this->company_details->id)
            ->first();
    }

    private function getBankTransferPaymentMethod($merchantId)
    {
        return model(PgPaymentMethodModel::class)
            ->where([
                'pg_merchant_id' => $merchantId,
                'payment_method' => PgPaymentMethod::BANK_TRANSFER,
                'company_id' => $this->company_details->id
            ])
            ->first();
    }

    private function getValidBankAccount($bankAccountId)
    {
        return model(BankAccountModel::class)
            ->select(['tb_autopay_bank_account.id', 'tb_autopay_bank.brand_name'])
            ->join('tb_autopay_bank', 'tb_autopay_bank.id = tb_autopay_bank_account.bank_id')
            ->where([
                'tb_autopay_bank_account.id' => $bankAccountId,
                'tb_autopay_bank_account.company_id' => $this->company_details->id,
                'tb_autopay_bank_account.active' => 1,
                'tb_autopay_bank_account.bank_api_connected' => 1,
            ])
            ->first();
    }

    private function isBidvEnterprise($bankAccount)
    {
        if ($bankAccount->brand_name !== 'BIDV') return false;

        return model(BidvEnterpriseAccountModel::class)
            ->where('bank_account_id', $bankAccount->id)
            ->first() !== null;
    }

    private function validateVARequirements($bankName, $useDynamicVA, $supportsDynamicVA, $bankSubAccountId)
    {
        if ($useDynamicVA && ! $supportsDynamicVA) {
            return ['valid' => false, 'message' => 'Tài khoản ngân hàng này không hỗ trợ VA động theo đơn hàng'];
        }

        $banksRequireVA = ['BIDV', 'MSB', 'KienLongBank', 'OCB'];
        $requiresVA = in_array($bankName, $banksRequireVA);

        if ($requiresVA && ! $useDynamicVA && empty($bankSubAccountId)) {
            return ['valid' => false, 'message' => "Ngân hàng {$bankName} bắt buộc phải chọn tài khoản ảo (VA)"];
        }

        return ['valid' => true];
    }

    private function profileExists($merchantId, $paymentMethodId, $bankAccountId)
    {
        return model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $merchantId,
                'pg_payment_method_id' => $paymentMethodId,
                'bank_account_id' => $bankAccountId,
                'type' => PgProfileType::BANK_ACCOUNT,
                'company_id' => $this->company_details->id,
            ])
            ->first() !== null;
    }

    private function clearDefaultProfiles($merchantId, $paymentMethodId)
    {
        model(PgProfileModel::class)
            ->where([
                'pg_merchant_id' => $merchantId,
                'pg_payment_method_id' => $paymentMethodId,
                'company_id' => $this->company_details->id,
            ])
            ->whereIn('type', [PgProfileType::BANK_ACCOUNT, PgProfileType::NAPAS_VIETQR])
            ->set(['default' => 0])
            ->update();
    }

    private function createProfile($merchantId, $paymentMethodId, $bankAccountId, $isDefault)
    {
        return model(PgProfileModel::class)->insert([
            'pg_merchant_id' => $merchantId,
            'pg_payment_method_id' => $paymentMethodId,
            'type' => PgProfileType::BANK_ACCOUNT,
            'bank_account_id' => $bankAccountId,
            'default' => $isDefault,
            'active' => true,
            'company_id' => $this->company_details->id,
        ]);
    }

    private function createBranchProfile($merchantId, $paymentMethodId, $profileId, $bankAccountId, $useDynamicVA, $bankSubAccountId, $bankName)
    {
        $pgBranch = model(PgBranchModel::class)
            ->where(['pg_merchant_id' => $merchantId, 'company_id' => $this->company_details->id, 'active' => 1])
            ->first();

        $pgTerminal = model(PgBranchTerminalModel::class)
            ->where(['pg_merchant_id' => $merchantId, 'company_id' => $this->company_details->id, 'active' => 1])
            ->first();

        if (! $pgBranch || ! $pgTerminal) return;

        $banksWithoutVA = ['TPBank', 'VPBank', 'VietinBank'];
        $forceMainAccount = in_array($bankName, $banksWithoutVA);

        $finalBankSubAccountId = null;
        if (! $forceMainAccount && ! $useDynamicVA && $bankSubAccountId) {
            $finalBankSubAccountId = $bankSubAccountId;
        }

        model(PgBranchProfileModel::class)->insert([
            'pg_merchant_id' => $merchantId,
            'pg_branch_id' => $pgBranch->id,
            'pg_branch_terminal_id' => $pgTerminal->id,
            'pg_payment_method_id' => $paymentMethodId,
            'pg_profile_id' => $profileId,
            'bank_account_id' => $bankAccountId,
            'bank_sub_account_id' => $finalBankSubAccountId,
            'use_dynamic_va' => $useDynamicVA ? 1 : 0,
            'company_id' => $this->company_details->id,
        ]);
    }

    private function getProfile($profileId, $merchantId)
    {
        return model(PgProfileModel::class)
            ->where([
                'id' => $profileId,
                'pg_merchant_id' => $merchantId,
                'company_id' => $this->company_details->id,
            ])
            ->first();
    }

    private function getPgBranch($merchantId)
    {
        return model(PgBranchModel::class)
            ->where([
                'pg_merchant_id' => $merchantId,
                'company_id' => $this->company_details->id,
                'active' => 1
            ])
            ->first();
    }

    private function bankSubAccountExists($bankSubAccountId)
    {
        return model(BankSubAccountModel::class)
            ->where('id', $bankSubAccountId)
            ->first() !== null;
    }

    private function saveOrUpdateBranchProfile($profile, $merchant, $pgBranch, $paymentMethod, $data)
    {
        $existingBranchProfile = model(PgBranchProfileModel::class)
            ->where([
                'pg_profile_id' => $profile->id,
                'company_id' => $this->company_details->id
            ])
            ->first();

        $branchProfileData = [
            'pg_merchant_id' => $merchant->id,
            'pg_branch_id' => $pgBranch->id,
            'pg_payment_method_id' => $paymentMethod->id,
            'pg_profile_id' => $profile->id,
            'bank_account_id' => $profile->bank_account_id,
            'bank_sub_account_id' => !empty($data['bank_sub_account_id']) ? $data['bank_sub_account_id'] : null,
            'use_dynamic_va' => ($data['use_dynamic_va'] ?? '0') === '1' ? 1 : 0,
            'company_id' => $this->company_details->id,
        ];

        if ($existingBranchProfile) {
            model(PgBranchProfileModel::class)
                ->where('id', $existingBranchProfile->id)
                ->set($branchProfileData)
                ->update();
            return true;
        } else {
            model(PgBranchProfileModel::class)->insert($branchProfileData);
            return false;
        }
    }
}
