<?php

namespace App\Controllers;

use Exception;
use App\Controllers\BaseController;
use App\Features\PaymentMethod\PaymentMethodFeature;
use App\Enums\PgPaymentMethod;

class Paymentmethods extends BaseController
{
    public function index()
    {
        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withOnboardingContext();

            $merchantContext = $feature->merchantContext();
            $onboardingContext = $feature->onboardingContext();

            $data = [
                'page_title' => 'Phương thức thanh toán',
                'user_details' => $this->user_details,
                'company_details' => $this->company_details,
                'merchant' => $merchantContext->merchant,
                'payment_methods' => $merchantContext->paymentMethods,
                'bank_accounts' => $merchantContext->bankAccounts,
                'onboarding_status' => $onboardingContext->onboardingStatus,
                'features' => $onboardingContext->features,
                'payment_method_exists' => $merchantContext->paymentMethodExistence
            ];

            return view('payment-methods/index', $data);
        } catch (Exception $e) {
            // return redirect()->back()->with('error', 'Có lỗi xảy ra: ' . $e->getMessage());
        }
    }

    public function banktransfer()
    {
        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withBankTransferContext();

            $merchantContext = $feature->merchantContext();
            $bankTransferContext = $feature->bankTransferContext();

            $data = [
                'page_title' => 'Chuyển khoản ngân hàng',
                'user_details' => $this->user_details,
                'company_details' => $this->company_details,
                'merchant' => $merchantContext->merchant,
                'payment_method' => $bankTransferContext->paymentMethod,
                'bankProfiles' => $bankTransferContext->bankProfiles,
                'available_bank_accounts' => $bankTransferContext->availableBankAccounts
            ];

            return view('payment-methods/bank-transfer', $data);
        } catch (Exception $e) {
            return redirect()->to('paymentmethods')->with('error', $e->getMessage());
        }
    }

    public function setDefaultBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'profile_id' => 'required|numeric',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors(),
            ]);
        }

        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withBankTransferContext();

            $bankTransferContext = $feature->bankTransferContext();

            $success = $bankTransferContext->setDefaultBankProfile((int) $data['profile_id']);

            if (! $success) {
                return $this->response->setJSON([
                    'status' => false,
                    'message' => 'Tài khoản thụ hưởng không tồn tại',
                ]);
            }

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã cập nhật tài khoản thụ hưởng mặc định thành công',
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function addBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();
        if (! $this->validate([
            'bank_account_id' => 'required|numeric',
            'is_default' => 'permit_empty|in_list[0,1]',
            'use_dynamic_va' => 'permit_empty|in_list[0,1]'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withBankTransferContext();

            $bankTransferContext = $feature->bankTransferContext();

            $bankAccount = $bankTransferContext->getValidBankAccount((int) $data['bank_account_id']);
            if (! $bankAccount) {
                return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng không hợp lệ']);
            }

            $supportsDynamicVA = $bankTransferContext->isBidvEnterprise($bankAccount);
            $useDynamicVA = ($data['use_dynamic_va'] ?? '0') === '1';

            $validation = $bankTransferContext->validateVARequirements($bankAccount->brand_name, $useDynamicVA, $supportsDynamicVA, $data['bank_sub_account_id'] ?? null);

            if (! $validation['valid']) {
                return $this->response->setJSON(['status' => false, 'message' => $validation['message']]);
            }

            if ($bankTransferContext->profileExists((int) $data['bank_account_id'])) {
                return $this->response->setJSON(['status' => false, 'message' => 'Tài khoản ngân hàng này đã được thêm vào tài khoản thụ hưởng']);
            }

            $isDefault = ($data['is_default'] ?? '0') === '1';

            if ($isDefault) {
                $bankTransferContext->clearDefaultProfiles();
            }

            $profileId = $bankTransferContext->createProfile((int) $data['bank_account_id'], $isDefault);
            $bankTransferContext->createBranchProfile($profileId, (int) $data['bank_account_id'], $useDynamicVA, $data['bank_sub_account_id'] ?? null, $bankAccount->brand_name);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã thêm tài khoản thụ hưởng thành công',
                'profile_id' => $profileId
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function removeBankProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'profile_id' => 'required|numeric'
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withBankTransferContext();

            $bankTransferContext = $feature->bankTransferContext();

            $bankTransferContext->removeProfile((int) $data['profile_id']);

            return $this->response->setJSON([
                'status' => true,
                'message' => 'Đã xóa tài khoản thụ hưởng thành công'
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function toggleFeature()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON([
                'status' => false,
                'message' => 'Yêu cầu không hợp lệ',
            ]);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'feature_id' => 'required',
            'action' => 'required|in_list[enable,disable]',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors(),
            ]);
        }

        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withOnboardingContext();

            $onboardingContext = $feature->onboardingContext();

            $result = $onboardingContext->toggleFeature($data['feature_id'], $data['action']);

            return $this->response->setJSON($result);
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function togglePaymentMethod()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $data = $this->request->getPost();

        if (! $this->validate([
            'payment_method' => 'required|in_list[' . implode(',', [PgPaymentMethod::BANK_TRANSFER, PgPaymentMethod::CARD]) . ']',
            'action' => 'required|in_list[enable,disable]',
        ])) {
            return $this->response->setJSON([
                'status' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withOnboardingContext();

            $merchantContext = $feature->merchantContext();
            $onboardingContext = $feature->onboardingContext();

            return $this->response->setJSON(
                $onboardingContext->togglePaymentMethod($merchantContext->merchant, $data['payment_method'], $data['action'])
            );
        } catch (Exception $e) {
            return $this->response->setJSON([
                'status' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function getBankSubAccounts()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        $bankAccountId = $this->request->getGet('bank_account_id');

        if (! is_numeric($bankAccountId)) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid bank account ID']);
        }

        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withBankTransferContext();

            $bankTransferContext = $feature->bankTransferContext();

            $bankAccount = $bankTransferContext->getValidBankAccount((int) $bankAccountId);
            if (! $bankAccount) {
                return $this->response->setJSON(['status' => false, 'message' => 'Bank account not found']);
            }

            $formattedSubAccounts = $bankTransferContext->getBankSubAccounts((int) $bankAccountId);

            return $this->response->setJSON([
                'status' => true,
                'data' => $formattedSubAccounts
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function saveVAProfile()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        // This method needs to be implemented with Feature pattern
        // For now, return not implemented
        return $this->response->setJSON([
            'status' => false,
            'message' => 'Method not implemented yet'
        ]);
    }

    public function getAllVAProfiles()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        // This method needs to be implemented with Feature pattern
        // For now, return not implemented
        return $this->response->setJSON([
            'status' => false,
            'message' => 'Method not implemented yet'
        ]);
    }

    public function checkBidvEnterpriseSupport()
    {
        if (! $this->request->isAJAX()) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid request']);
        }

        if (! is_numeric($bankAccountId = $this->request->getGet('bank_account_id'))) {
            return $this->response->setJSON(['status' => false, 'message' => 'Invalid bank account ID']);
        }

        try {
            $feature = new PaymentMethodFeature($this->company_details->id);
            $feature->withMerchantContext()->withBankTransferContext();

            $bankTransferContext = $feature->bankTransferContext();

            if (! ($bankAccount = $bankTransferContext->getValidBankAccount((int) $bankAccountId))) {
                return $this->response->setJSON(['status' => false, 'message' => 'Bank account not found']);
            }

            $supportsDynamicVA = $bankTransferContext->isBidvEnterprise($bankAccount);

            return $this->response->setJSON([
                'status' => true,
                'supports_dynamic_va' => $supportsDynamicVA,
                'bank_name' => $bankAccount->brand_name,
            ]);
        } catch (Exception $e) {
            return $this->response->setJSON(['status' => false, 'message' => $e->getMessage()]);
        }
    }
}
