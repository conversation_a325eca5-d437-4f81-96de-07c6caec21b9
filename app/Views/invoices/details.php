<style>
.copyjs {
    cursor: pointer;
}
</style>
<main class="content">
    <div class="container-fluid">
        <div class="row mx-auto" style="max-width:800px">
            <h1 class="h3 my-3">Hóa đơn #<?= esc($invoice_details->id);?></h1>

            <div class="col-12">
                <div class="card">
                    <div class="card-body m-sm-3 m-md-5">
                        <div class="mb-4">
                            <img src="https://sepay.vn/assets/img/logo/sepay-blue-154x50.png" class="img-fluid">
                        </div>

                        <div class="my-3">
                            <?php if($invoice_details->status == "Paid") { ?>
                            <h1 class="text-center"><span class="badge bg-success rounded-pill">Đã thanh toán</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Unpaid") { ?>
                            <h1 class="text-center"><span class="badge bg-danger  rounded-pill">Ch<PERSON><PERSON> thanh toán</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Cancelled") { ?>
                            <h1 class="text-center"><span class="badge bg-secondary  rounded-pill">Đã hủy</span>
                            </h1>

                            <?php } else if($invoice_details->status == "Refunded") { ?>
                            <h1 class="text-center"><span class="badge bg-secondary  rounded-pill">Đã hoàn tiền</span>
                            </h1>

                            <?php } ?>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <div class="text-muted">Hóa đơn số</div>
                                <strong>#<?= esc($invoice_details->id);?></strong>
                            </div>
                            <div class="col-6 text-md-right">
                                <div class="text-muted">Ngày tạo</div>
                                <strong><?= esc($invoice_details->date);?></strong>
                            </div>
                        </div>

                        <hr class="my-4" />

                        <div class="row">
                            <div class="col-8 col-md-6">
                                <div class="text-muted">Bên mua</div>
                                <strong>
                                    <?= esc($company_details->short_name);?>
                                </strong>
                                <p class="mb-0">
                                    <?= esc($company_details->full_name);?>
                                </p>
                            </div>
                            <div class="col-4  col-md-6 text-md-right">
                                <div class="text-muted">Bên bán</div>
                                <strong>
                                    SePay
                                </strong>
                            </div>
                        </div>

                        <hr class="my-4" />

                        <div class="row mb-4">
                            <div class="col-8 col-md-6">
                                <div class="text-muted">Phương thức thanh toán</div>
                                <?php if ($invoice_details->payment_method == 'BankTransfer' || $invoice_details->payment_method == null): ?>
                                    <strong>
                                        Chuyển khoản ngân hàng
                                    </strong>
                                <?php elseif ($invoice_details->payment_method == 'Card'): ?>
                                    <strong>
                                        Thẻ tín dụng/ghi nợ
                                    </strong>
                                    <?php if ($invoice_details->payment_info && $paymentInfo = json_decode($invoice_details->payment_info)): ?>
                                        <div class="font-mono d-flex flex-row align-items-center gap-1 text-muted">
                                            <div style="width: 32px; flex: none; text-align: center;">
                                                <?php if ($paymentInfo->card_brand === 'VISA'): ?>
                                                    <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><g><rect stroke="#DDD" fill="#FFF" x=".25" y=".25" width="23.5" height="15.5" rx="2"></rect><path d="M2.788 5.914A7.201 7.201 0 0 0 1 5.237l.028-.125h2.737c.371.013.672.125.77.519l.595 2.836.182.854 1.666-4.21h1.799l-2.674 6.167H4.304L2.788 5.914Zm7.312 5.37H8.399l1.064-6.172h1.7L10.1 11.284Zm6.167-6.021-.232 1.333-.153-.066a3.054 3.054 0 0 0-1.268-.236c-.671 0-.972.269-.98.531 0 .29.365.48.96.762.98.44 1.435.979 1.428 1.681-.014 1.28-1.176 2.108-2.96 2.108-.764-.007-1.5-.158-1.898-.328l.238-1.386.224.099c.553.23.917.328 1.596.328.49 0 1.015-.19 1.022-.604 0-.27-.224-.466-.882-.769-.644-.295-1.505-.788-1.491-1.674C11.878 5.84 13.06 5 14.74 5c.658 0 1.19.138 1.526.263Zm2.26 3.834h1.415c-.07-.308-.392-1.786-.392-1.786l-.12-.531c-.083.23-.23.604-.223.59l-.68 1.727Zm2.1-3.985L22 11.284h-1.575s-.154-.71-.203-.926h-2.184l-.357.926h-1.785l2.527-5.66c.175-.4.483-.512.889-.512h1.316Z" fill="#1434CB"></path></g></svg>
                                                <?php elseif ($paymentInfo->card_brand === 'MASTERCARD'): ?>
                                                    <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><rect fill="#252525" height="16" rx="2" width="24"></rect><circle cx="9" cy="8" fill="#eb001b" r="5"></circle><circle cx="15" cy="8" fill="#f79e1b" r="5"></circle><path d="M12 4c1.214.912 2 2.364 2 4s-.786 3.088-2 4c-1.214-.912-2-2.364-2-4s.786-3.088 2-4z" fill="#ff5f00"></path></svg>
                                                <?php elseif ($paymentInfo->card_brand === 'JCB'): ?>
                                                    <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon p-CardBrandIcon--visible"><path d="M0 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0H2.4C1.308 0 0 1.195 0 3.2V16Z" fill="#047AB1"></path><path d="M2.724 10.816c-.922 0-1.838-.115-2.724-.341V9.3c.687.378 1.473.591 2.28.619.924 0 1.44-.576 1.44-1.365V5.333H6v3.222c0 1.258-.744 2.261-3.276 2.261Z" fill="#fff"></path><path d="M8.4 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8C9.708 0 8.4 1.195 8.4 3.2V16Z" fill="#D42D06"></path><path d="M8.4 6.08c.696-.597 1.896-.97 3.84-.885 1.056.042 2.16.32 2.16.32v1.184a5.313 5.313 0 0 0-2.076-.608C10.848 5.973 9.948 6.709 9.948 8c0 1.29.9 2.027 2.376 1.92a5.387 5.387 0 0 0 2.076-.619v1.174s-1.104.288-2.16.33c-1.944.086-3.144-.288-3.84-.885V6.08Z" fill="#fff"></path><path d="M16.8 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8c-1.092 0-2.4 1.195-2.4 3.2V16Z" fill="#67B637"></path><path d="M22.8 9.28c0 .853-.744 1.387-1.74 1.387H16.8V5.333h3.876l.276.011c.876.043 1.524.501 1.524 1.29 0 .62-.444 1.153-1.248 1.28v.033C22.116 8 22.8 8.5 22.8 9.28Zm-3.06-3.104a1.226 1.226 0 0 0-.156-.01h-1.44v1.343h1.596c.3-.064.552-.309.552-.672a.657.657 0 0 0-.552-.661Zm.18 2.176a1.16 1.16 0 0 0-.192-.01h-1.584v1.46h1.584l.192-.02a.716.716 0 0 0 .552-.715c0-.374-.24-.64-.552-.715Z" fill="#fff"></path></svg>
                                                <?php else: ?>
                                                    <div class="border rounded-lg">UNK</div>
                                                <?php endif; ?>
                                            </div>
                                            <?= str_replace('x', '&bull;', $paymentInfo->card_number) ?></p>
                                        </div>
                                    <?php endif ?>
                                <?php endif; ?>
                            </div>
                            <?php if ($invoice_details->auto_payment == 1): ?>
                            <div class="col-8 col-md-6">
                                <div class="text-muted">Tự động</div>
                                    <strong><i class="bi bi-check-circle-fill text-success"></i> Thanh toán tự động</strong>
                            </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <?php if($invoice_details->status == "Unpaid"): ?>

                        <div class="p-3 border" id="container-qrcode">
                            <h5 class="text-center">Hướng dẫn thanh toán</h5>
                            <div class="row">
                                <div class="col-md-5 text-center">

                                    <img src="<?= $qrcode;?>" class="img-fluid">
                                    <a href="javascript:void(0)" onclick="downloadQRCode()" class="btn btn-outline-primary btn-sm mt-2">
                                        <i class="bi bi-download"></i> Tải ảnh QR
                                    </a>

                                </div>
                                <div class="col-md-7">
                                    <table class="table table-borderless">
                                        <tbody>
                                            <tr>
                                                <td>Ngân hàng</td>
                                                <td class="fw-bold">MBBank</td>
                                            </tr>
                                            <tr>
                                                <td>Số tài khoản</td>
                                                <td class="fw-bold"><span id="vcb_id">*************</span> <i
                                                        class="bi bi-files copyjs" data-clipboard-target="#vcb_id"
                                                        id="i_vcb_id" data-bs-toggle="tooltip"
                                                        data-bs-title="Đã copy"></i></td>
                                            </tr>
                                            <tr>
                                                <td>Thụ hưởng</td>
                                                <td class="fw-bold">SEPAY JSC</td>
                                            </tr>

                                            <tr>
                                                <td>Nội dung CK</td>
                                                <td class="fw-bold"><span id="trans_content"><?= esc($paycode);?></span> <i class="bi bi-files copyjs" data-clipboard-target="#trans_content"
                                                        id="i_trans_content" data-bs-toggle="tooltip"
                                                        data-bs-title="Đã copy"></i>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Số tiền</td>
                                                <td class="fw-bold"><?= number_format($invoice_details->total);?> đ
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="2"><i class="bi bi-qr-code-scan"></i> Dùng ứng dụng ngân
                                                    hàng quét mã
                                                    QR để chuyển khoản</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                </div>
                            </div>
                            <div class="alert alert-secondary alert-dismissible" role="alert">
                                <div class="alert-message">
                                    Vui lòng <strong>giữ nguyên nội dung chuyển khoản</strong>. Hệ thống sẽ tự nhận
                                    diện
                                    thanh toán trong vài giây
                                </div>
                            </div>
                        </div>

                        <?php if ($company_details->credit_balance > 0): ?>
                        <?= component('_components/billing/credit-apply-form', ['invoice_details' => $invoice_details, 'company_details' => $company_details]) ?>
                        <?php endif ?>

                        <?php if (pg_billing_enabled()): ?>
                            <?= component('_components/billing/payment-gateway', [
                                'invoice' => $invoice_details,
                                'company' => $company_details,
                                'invoice_items' => $invoice_items
                            ]) ?>
                        <?php endif ?>

                        <?php endif;?>

                        <table class="table table-sm mt-5">
                            <thead>
                                <tr>
                                    <th>Mô tả</th>
                                    <th>Thuế</th>
                                    <th class="text-end">Giá tiền</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($invoice_items as $item): ?>
                                <tr>
                                    <td><?= esc($item->description);?> <?php if($invoice_details->type=='Excess') {?><br> <a class='ms-2' target='_blank' href='<?= base_url('statistics/counter?period=custom&start_date=' . esc($item->start_date) . '&end_date=' . esc($item->end_date));?>'> <i class="bi bi-chevron-right"></i> Xem thống kê vượt <i class="bi bi-bar-chart-line"></i></a> <?php } ?>
                                        <?php if ($item->details): ?>
                                            <?= $item->details ?>
                                        <?php endif ?>
                                    </td>
                                    <td><?php if($item->taxed == 1) echo 'Có'; else echo 'Không'; ?></td>

                                    <td class="text-end" style="white-space: nowrap;"><?= number_format($item->amount);?> đ</td>
                                </tr>
                                <?php endforeach; ?>

                                
                            </tbody>
                            
                        </table>
                        <div class="row">
                            <div class="col-md-6"></div>
                            <div class="col-md-6">
                                <table class="table">
                                    <tbody>
                                        <tr>
                                            <td class="text-end fw-bold">Tạm tính</td>
                                            <td class="text-end"><?= number_format($invoice_details->subtotal);?> đ</td>
                                            
                                        </tr>
                                        <?php if ($invoice_details->credit): ?>
                                        <tr>
                                            <td class="text-end fw-bold">Tín dụng</td>
                                            <td class="text-end">-<?= number_format($invoice_details->credit) ?> đ</td>
                                        </tr>
                                        <?php endif ?>

                                        <tr>
                                            <td class="text-end  fw-bold">Thuế VAT (<?= number_format($invoice_details->tax_rate);?>%)</td>
                                            <td class="text-end"><?= number_format($invoice_details->tax);?> đ</td>
                                            
                                        </tr>
                                        <tr>
                                            <td class="text-end  fw-bold">Tổng</td>
                                            <td class="text-end  fw-bold"><?= number_format($invoice_details->total);?> đ</td>
                                            
                                        </tr>
                                    </tbody>
                                </table>
                                <?php if ($canRequestVatInvoice): ?>
                                    <?php if (! $invoice_details->vat_invoice_requested_at): ?>
                                        <div class="text-end mt-2">
                                            <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#vatNoteModal">
                                                <i class="bi bi-receipt me-1"></i> Yêu cầu xuất hóa đơn VAT
                                            </button>
                                        </div>
                                    <?php elseif ($invoice_details->vat_invoice_requested_at && ! $invoice_details->tax_issued): ?>
                                        <div class="text-warning text-end mb-2">
                                            <i class="bi bi-check-circle me-1"></i> Đã gửi yêu cầu xuất hóa đơn VAT
                                        </div>
                                    <?php elseif ($invoice_details->tax_issued): ?>
                                        <div class="text-success text-end mb-2">
                                            <i class="bi bi-check-circle me-1"></i> Đã xuất hóa đơn VAT
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row mt-5">
                            <div class="col-6 text-start"><a href="<?= base_url('invoices');?>"><i class="bi bi-chevron-left"></i> Quay lại</a></div>
                            <div class="col-6 text-end">
                                <a onclick="window.print()">
                                <i class="bi bi-printer"></i> In
                            </a></div>
                           
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</main>

<?php include(APPPATH . 'Views/templates/autopay/inc_footer.php');?>

<script src="<?php echo base_url();?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url();?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url();?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url();?>/assets/js/app.js?v=1"></script>
<script src="<?php echo base_url();?>/assets/clipboardjs/clipboard.min.js"></script>

<script>
    const vcbTooltip = document.getElementById('i_vcb_id');
    const transTooltip = document.getElementById('i_trans_content');

    if (vcbTooltip) {
        const tooltip = new bootstrap.Tooltip(vcbTooltip);
        tooltip.disable();
    }

    if (transTooltip) {
        const tooltip = new bootstrap.Tooltip(transTooltip);
        tooltip.disable();
    }


var clipboard = new ClipboardJS('.copyjs');

clipboard.on('success', function(e) {


    id = e.trigger.getAttribute('id');

    tooltip = new bootstrap.Tooltip(document.getElementById(id), {
        trigger: 'click'
    });
    tooltip.show();

    setTimeout(function() {
        tooltip.hide();
        tooltip.disable();

    }, 500);
});

<?php if($invoice_details->status == 'Unpaid'): ?>

function check_invoice_status() {
    $.ajax({
        url : "<?= base_url('invoices/ajax_check_status');?>",
        type: "POST",
        data: {invoice_id: <?= $invoice_details->id;?>, "<?php echo csrf_token() ?>": "<?php echo csrf_hash() ?>"},
        dataType: "JSON",
        success: function(data)
        {
            //if success close modal and reload ajax table
            if(data.status == true && data.invoice_status == 'Paid') {
                notyf.success('Thanh toán thành công!');
                location.reload();
            }  
        }
        
    });
}

setInterval( function () {
    check_invoice_status();
}, 3000 );

function downloadQRCode() {
        const qrImageSrc = "<?= $qrcode; ?>";
        if (qrImageSrc) {
            let img = new Image();
            img.crossOrigin = "anonymous";
            img.src = qrImageSrc;

            img.onload = function() {
                let canvas = document.createElement("canvas");
                let ctx = canvas.getContext("2d");

                canvas.width = img.width;
                canvas.height = img.height;

                ctx.drawImage(img, 0, 0);

                canvas.toBlob(blob => {
                    let url = URL.createObjectURL(blob);
                    let a = document.createElement('a');
                    a.href = url;
                    a.download = "qr-invoice-<?= esc($invoice_details->id); ?>.png";
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, "image/png");
            };

            img.onerror = function() {
                notyf.error({
                    message: "Không thể tải hình ảnh QR."
                });
            };
        } else {
            notyf.error({
                message: "Không tìm thấy hình ảnh QR để tải xuống."
            });
        }
    }

<?php endif;?>
</script>

<?php include(APPPATH . 'Views/company/issue-vat-invoice.php'); ?>


<?php if ($invoice_details->status == 'Unpaid' && $company_details->credit_balance > 0): ?>
<?= component_script('_components/billing/credit-apply-form') ?>
<?php endif;?>

<?php if ($invoice_details->status == 'Unpaid' && pg_billing_enabled()): ?>
<?= component_script('_components/billing/payment-gateway') ?>
<?php endif ?>