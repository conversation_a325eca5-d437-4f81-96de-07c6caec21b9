<?php

use App\Config\Invoice;
use App\Features\BillingFeature;
use App\Features\PaymentGateway\PaymentGatewayFeature;
use App\Models\InvoiceModel;
use App\Models\PgAgreementModel;
use App\Models\PgCardModel;
use App\Models\PgCustomerModel;
use App\Models\PgMerchantModel;

$billingFeature = new BillingFeature;

if (!$billingFeature->pgEnabled) return;

$paymentGatewayFeature = new PaymentGatewayFeature;
$pgMerchant = model(PgMerchantModel::class)->where('merchant_id', $billingFeature->pgMerchantId)->first();

if (!$pgMerchant) return;

$pgCustomer = model(PgCustomerModel::class)
    ->where('pg_merchant_id', $pgMerchant->id)
    ->where('customer_id', $company_details->id)
    ->first();
    
$pgAgreement = $pgCustomer ? model(PgAgreementModel::class)
    ->join('tb_autopay_pg_order', 'tb_autopay_pg_order.id = tb_autopay_pg_agreement.pg_order_id')
    ->join('tb_autopay_company_subscription', 'tb_autopay_company_subscription.pg_agreement_id = tb_autopay_pg_agreement.id')
    ->where('tb_autopay_pg_agreement.pg_merchant_id', $pgMerchant->id)
    ->where('tb_autopay_pg_agreement.pg_customer_id', $pgCustomer->id)
    ->where('tb_autopay_pg_agreement.active', 1)
    ->first() : null;

$pgCard = $pgAgreement && $pgCustomer ? model(PgCardModel::class)
    ->where('pg_merchant_id', $pgMerchant->id)
    ->where('pg_customer_id', $pgCustomer->id)
    ->where('id', $pgAgreement->pg_card_id)
    ->first() : null;    
    
$unpaidInvoices = model(InvoiceModel::class)
    ->where('company_id', $company_details->id)
    ->where('status', 'Unpaid')
    ->findAll();

?>

<?php if (isset($style) && $style): ?>
    <style>
        
    </style>

    <?php unset($style);
    return; ?>
<?php endif; ?>

<?php if (isset($script) && $script): ?>
    <?php if ($pgAgreement && $pgAgreement->auto_renew): ?>
    <script>
        var cancelAutoRenewModal = new bootstrap.Modal(document.getElementById('cancelAutoRenewModal'), {
            keyboard: false
        });
        
        $('#confirmCancelAutoRenew').on('click', () => {
            $.ajax({
                url: `<?= base_url('company/ajax_cancel_auto_renew_subscription') ?>`,
                method: 'POST',
                data: {
                    <?= csrf_token() ?>: `<?= csrf_hash() ?>`
                },
                success: (res) => {
                    window.location.reload()
                },
                error: (err) => {
                    notyf.error({
                        message: err.responseJSON.messages.error ?? 'Đã có lỗi xảy ra, vui lòng liên hệ SePay để được hỗ trợ',
                        dismissible: true
                    });
                    cancelAutoRenewModal.hide()
                }
            })
        })
    </script>
    <?php endif ?>
    <?php unset($script);
    return; ?>
<?php endif; ?>

<?php if (!$pgAgreement): ?>
    <?php if (count($unpaidInvoices) > 0): ?>
        <div class="alert alert-warning mb-0 text-sm">
            <div class="alert-message p-2">
                <p class="mb-2">Đang có hóa đơn gia hạn cần thanh toán</p>
                <a href="<?= base_url('invoices/details/' . $unpaidInvoices[0]->id) ?>" class="btn btn-sm btn-warning rounded">Thanh toán</a>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-info mb-0 text-sm">
            <div class="alert-message p-2">
                <i class="bi bi-info-circle-fill text-primary"></i> Kích hoạt gia hạn tự động qua thẻ tín dụng/ghi nợ tại hóa đơn gia hạn sắp tới sau ngày <?= date('d/m/Y', strtotime($subscription->end_date)) ?>.
            </div>
        </div>
    <?php endif ?>
</div>
<?php else: ?>
<div>
    <?php if ($pgAgreement->auto_renew): ?>
    <div class="alert alert-info text-sm">
        <div class="alert-message p-2">
            Tự động gia hạn vào ngày <?= date('d/m/Y', strtotime($pgAgreement->next_due_date)) ?>.
        </div>
    </div>
    <?php else: ?>
    <div class="alert alert-warning">
        <div class="alert-message p-2">
            <p class="fw-bold mb-1"><i class="bi bi-x-circle-fill text-danger me-1"></i> Gia hạn tự động đã bị hủy</p>
            <p class="mb-0 text-sm">Đăng ký lại gia hạn tự động vào hóa đơn gia hạn sắp tới sau ngày <?= date('d/m/Y', strtotime($pgAgreement->next_due_date)) ?>.</p>
        </div>
    </div>
    <?php endif ?>
    
    <?php if ($pgCard): ?>
    <div>
        <p class="text-sm mb-1 fw-bold">Phương thức thanh toán</p>
        <div class="d-flex align-items-center gap-3 border rounded-lg text-left p-3">
            <div style="width: 40px; flex: none;">
                <?php if ($pgCard->card_brand === 'VISA'): ?>
                    <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><g><rect stroke="#DDD" fill="#FFF" x=".25" y=".25" width="23.5" height="15.5" rx="2"></rect><path d="M2.788 5.914A7.201 7.201 0 0 0 1 5.237l.028-.125h2.737c.371.013.672.125.77.519l.595 2.836.182.854 1.666-4.21h1.799l-2.674 6.167H4.304L2.788 5.914Zm7.312 5.37H8.399l1.064-6.172h1.7L10.1 11.284Zm6.167-6.021-.232 1.333-.153-.066a3.054 3.054 0 0 0-1.268-.236c-.671 0-.972.269-.98.531 0 .29.365.48.96.762.98.44 1.435.979 1.428 1.681-.014 1.28-1.176 2.108-2.96 2.108-.764-.007-1.5-.158-1.898-.328l.238-1.386.224.099c.553.23.917.328 1.596.328.49 0 1.015-.19 1.022-.604 0-.27-.224-.466-.882-.769-.644-.295-1.505-.788-1.491-1.674C11.878 5.84 13.06 5 14.74 5c.658 0 1.19.138 1.526.263Zm2.26 3.834h1.415c-.07-.308-.392-1.786-.392-1.786l-.12-.531c-.083.23-.23.604-.223.59l-.68 1.727Zm2.1-3.985L22 11.284h-1.575s-.154-.71-.203-.926h-2.184l-.357.926h-1.785l2.527-5.66c.175-.4.483-.512.889-.512h1.316Z" fill="#1434CB"></path></g></svg>
                <?php elseif ($pgCard->card_brand === 'MASTERCARD'): ?>
                    <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon"><rect fill="#252525" height="16" rx="2" width="24"></rect><circle cx="9" cy="8" fill="#eb001b" r="5"></circle><circle cx="15" cy="8" fill="#f79e1b" r="5"></circle><path d="M12 4c1.214.912 2 2.364 2 4s-.786 3.088-2 4c-1.214-.912-2-2.364-2-4s.786-3.088 2-4z" fill="#ff5f00"></path></svg>
                <?php elseif ($pgCard->card_brand === 'JCB'): ?>
                    <svg viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg" role="presentation" focusable="false" class="p-Logo p-Logo--md p-CardBrandIcon p-CardBrandIcon--visible"><path d="M0 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0H2.4C1.308 0 0 1.195 0 3.2V16Z" fill="#047AB1"></path><path d="M2.724 10.816c-.922 0-1.838-.115-2.724-.341V9.3c.687.378 1.473.591 2.28.619.924 0 1.44-.576 1.44-1.365V5.333H6v3.222c0 1.258-.744 2.261-3.276 2.261Z" fill="#fff"></path><path d="M8.4 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8C9.708 0 8.4 1.195 8.4 3.2V16Z" fill="#D42D06"></path><path d="M8.4 6.08c.696-.597 1.896-.97 3.84-.885 1.056.042 2.16.32 2.16.32v1.184a5.313 5.313 0 0 0-2.076-.608C10.848 5.973 9.948 6.709 9.948 8c0 1.29.9 2.027 2.376 1.92a5.387 5.387 0 0 0 2.076-.619v1.174s-1.104.288-2.16.33c-1.944.086-3.144-.288-3.84-.885V6.08Z" fill="#fff"></path><path d="M16.8 16h4.8c1.092 0 2.4-1.195 2.4-2.133V0h-4.8c-1.092 0-2.4 1.195-2.4 3.2V16Z" fill="#67B637"></path><path d="M22.8 9.28c0 .853-.744 1.387-1.74 1.387H16.8V5.333h3.876l.276.011c.876.043 1.524.501 1.524 1.29 0 .62-.444 1.153-1.248 1.28v.033C22.116 8 22.8 8.5 22.8 9.28Zm-3.06-3.104a1.226 1.226 0 0 0-.156-.01h-1.44v1.343h1.596c.3-.064.552-.309.552-.672a.657.657 0 0 0-.552-.661Zm.18 2.176a1.16 1.16 0 0 0-.192-.01h-1.584v1.46h1.584l.192-.02a.716.716 0 0 0 .552-.715c0-.374-.24-.64-.552-.715Z" fill="#fff"></path></svg>
                <?php else: ?>
                    <div class="border border-slate-100 px-3 py-2 rounded-lg">UNK</div> 
                <?php endif ?>
            </div>
            <div>
                <p class="fw-bold font-mono mb-0"><?= str_replace('x', '&bull;', $pgCard->card_number) ?></p>
                <p class="font-mono mb-0"><?= $pgCard->card_holder_name ?></p>
                <p class="text-sm mb-0">Hết hạn <?= substr($pgCard->card_expiry, 0, 2) . '/' . substr($pgCard->card_expiry, 2, 4) ?></p>
            </div>
            <div class="ms-auto">
                <?php if ($pgCard->status === 'VALID'): ?>
                    <span class="badge bg-success">Hợp lệ</span>
                <?php else: ?>
                    <span class="badge bg-warning">Phiên hết hạn</span>
                <?php endif ?>
            </div>
        </div>
    </div>
    <?php endif ?>
    
    <?php if ($pgAgreement->auto_renew): ?>
        <button type="button" class="btn btn-outline-secondary mt-2 btn-sm rounded" data-bs-toggle="modal" data-bs-target="#cancelAutoRenewModal">Hủy gia hạn tự động</button>

        <!-- Modal -->
        <div class="modal fade" id="cancelAutoRenewModal" tabindex="-1" aria-labelledby="cancelAutoRenewModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-sm modal-dialog-centered">
                <div class="modal-content modal-sm border-0">
                    <div class="modal-header bg-gradient-warning text-dark">
                        <h5 class="modal-title fw-bold" id="cancelAutoRenewModalLabel">Xác nhận hủy gia hạn tự động</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-0">Hủy gia hạn tự động đồng nghĩa bạn phải tự thanh toán các hóa đơn gia hạn tiếp theo. Hành động này không thể hoàn tác.</p>
                    </div>
                    <div class="modal-footer d-flex justify-content-end gap-1">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">Đóng</button>
                        <button type="button" class="btn btn-danger" id="confirmCancelAutoRenew">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true" style="display: none;"></span> Xác nhận hủy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif ?>
</div>
<?php endif ?>