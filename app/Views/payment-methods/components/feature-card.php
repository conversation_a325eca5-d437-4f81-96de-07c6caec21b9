<?php
/**
 * Feature Card Component
 * 
 * @param array $feature - Feature data
 */
?>

<div class="col">
    <div class="card h-100 border-0 shadow-sm">
        <div class="card-body">
            <div class="d-flex align-items-start mb-3">
                <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                    <i class="<?= esc($feature['icon']) ?> text-primary"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="card-title mb-1"><?= esc($feature['name']) ?></h6>
                    <div class="d-flex align-items-center">
                        <?php if ($feature['status'] === 'available'): ?>
                            <span class="badge bg-success me-2">Đã kích hoạt</span>
                        <?php elseif ($feature['status'] === 'pending'): ?>
                            <span class="badge bg-warning me-2">Đang chờ duyệt</span>
                        <?php elseif ($feature['status'] === 'submitted'): ?>
                            <span class="badge bg-info me-2">Đã nộp hồ sơ</span>
                        <?php else: ?>
                            <span class="badge bg-secondary me-2">Chưa kích hoạt</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <p class="card-text text-muted small mb-3"><?= esc($feature['description']) ?></p>
            
            <?php if (isset($feature['features']) && is_array($feature['features'])): ?>
                <ul class="list-unstyled small text-muted mb-3">
                    <?php foreach ($feature['features'] as $item): ?>
                        <li class="mb-1">
                            <i class="bi bi-check-circle-fill text-success me-1"></i>
                            <?= esc($item) ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        
        <div class="card-footer bg-transparent border-0 pt-0">
            <?php if ($feature['status'] === 'available'): ?>
                <button class="btn btn-outline-danger btn-sm feature-toggle" 
                        data-feature="<?= esc($feature['id']) ?>" 
                        data-action="disable">
                    <i class="bi bi-pause-circle me-1"></i>
                    Tạm ngưng
                </button>
            <?php elseif ($feature['status'] === 'pending' || $feature['status'] === 'submitted'): ?>
                <button class="btn btn-outline-secondary btn-sm" disabled>
                    <i class="bi bi-clock me-1"></i>
                    Đang xử lý
                </button>
            <?php else: ?>
                <button class="btn btn-primary btn-sm feature-toggle" 
                        data-feature="<?= esc($feature['id']) ?>" 
                        data-action="enable">
                    <i class="bi bi-play-circle me-1"></i>
                    Kích hoạt
                </button>
            <?php endif; ?>
        </div>
    </div>
</div>
