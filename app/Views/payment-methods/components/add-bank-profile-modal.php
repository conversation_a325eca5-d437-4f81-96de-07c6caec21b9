<!-- Add Bank Profile Modal -->
<div class="modal fade" id="addBankProfileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm tài khoản thụ hưởng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addBankProfileForm">
                    <div class="mb-3">
                        <label for="bankAccountSelect" class="form-label">Chọn tài khoản ngân hàng</label>
                        <select class="form-select" id="bankAccountSelect" name="bank_account_id" required>
                            <option value="">-- Chọn tài khoản ngân hàng --</option>
                            <?php foreach ($available_bank_accounts as $account): ?>
                                <option value="<?= $account->id ?>" 
                                        data-bank-name="<?= esc($account->brand_name) ?>"
                                        data-supports-dynamic-va="false">
                                    <?= esc($account->brand_name) ?> - <?= esc($account->account_number) ?> (<?= esc($account->account_holder_name) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isDefaultProfile" name="is_default" value="1">
                            <label class="form-check-label" for="isDefaultProfile">
                                Đặt làm tài khoản thụ hưởng mặc định
                            </label>
                        </div>
                    </div>

                    <!-- VA Configuration Section -->
                    <div id="vaConfigSection" style="display: none;">
                        <hr>
                        <h6>Cấu hình Virtual Account (VA)</h6>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="useDynamicVA" name="use_dynamic_va" value="1">
                                <label class="form-check-label" for="useDynamicVA">
                                    Sử dụng VA động theo đơn hàng
                                </label>
                                <div class="form-text">
                                    VA động sẽ tạo số tài khoản ảo riêng cho từng đơn hàng
                                </div>
                            </div>
                        </div>

                        <div class="mb-3" id="bankSubAccountSection" style="display: none;">
                            <label for="bankSubAccountSelect" class="form-label">Chọn tài khoản ảo (VA)</label>
                            <select class="form-select" id="bankSubAccountSelect" name="bank_sub_account_id">
                                <option value="">-- Chọn tài khoản ảo --</option>
                            </select>
                            <div class="form-text">
                                Chọn tài khoản ảo để nhận thanh toán. Nếu không chọn, sẽ sử dụng tài khoản chính.
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="btnSaveBankProfile">
                    <span class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                    Thêm tài khoản
                </button>
            </div>
        </div>
    </div>
</div>
