<?php
/**
 * Bank Profile Item Component
 *
 * @param object $profile - Bank profile data
 * @param bool $isDefault - Whether this is the default profile
 */

// Ensure variables are set with defaults
if (!isset($profile)) return;
if (!isset($isDefault)) $isDefault = false;
?>

<div class="list-group-item" data-profile-id="<?= $profile->id ?>">
    <div class="d-flex flex-column flex-sm-row gap-3">
        <div class="flex-shrink-0">
            <div class="d-flex align-items-center">
                <?php if (!empty($profile->logo_path)): ?>
                    <img src="<?= esc($profile->logo_path) ?>" alt="<?= esc($profile->brand_name) ?>" class="me-2" style="height: 24px;">
                <?php else: ?>
                    <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                        <i class="bi bi-bank text-primary"></i>
                    </div>
                <?php endif; ?>
                <div>
                    <h6 class="mb-0"><?= esc($profile->brand_name) ?></h6>
                    <small class="text-muted"><?= esc($profile->account_number) ?></small>
                </div>
            </div>
        </div>
        
        <div class="flex-grow-1">
            <div class="d-flex flex-column">
                <div class="mb-1">
                    <strong><?= esc($profile->account_holder_name) ?></strong>
                    <?php if ($profile->default): ?>
                        <span class="badge bg-success ms-2">Mặc định</span>
                    <?php endif; ?>
                </div>
                <div class="text-muted small">
                    <?php if (isset($profile->type) && $profile->type === 'NAPAS_VIETQR'): ?>
                        <i class="bi bi-qr-code me-1"></i>NAPAS VietQR
                    <?php else: ?>
                        <i class="bi bi-bank me-1"></i>Tài khoản ngân hàng
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="flex-shrink-0">
            <div class="dropdown">
                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <?php if (!$profile->default): ?>
                        <li>
                            <button class="dropdown-item btn-set-default" data-profile-id="<?= $profile->id ?>">
                                <i class="bi bi-star me-2"></i>Đặt làm mặc định
                            </button>
                        </li>
                    <?php endif; ?>
                    <li>
                        <button class="dropdown-item text-danger btn-remove-profile" data-profile-id="<?= $profile->id ?>">
                            <i class="bi bi-trash me-2"></i>Xóa
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
