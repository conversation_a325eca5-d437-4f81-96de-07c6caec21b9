<?php
/**
 * Payment Method Item Component
 * 
 * @param array $method - Payment method data
 * @param bool $exists - Whether payment method exists
 * @param bool $isActive - Whether payment method is active
 * @param string $linkUrl - URL for clickable item (optional)
 */
?>

<?php if (isset($linkUrl) && $linkUrl): ?>
    <a href="<?= $linkUrl ?>" class="list-group-item list-group-item-action">
<?php else: ?>
    <div class="list-group-item">
<?php endif; ?>
    
    <div class="d-flex align-items-start">
        <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
            <i class="<?= $method['icon'] ?> text-primary fs-4"></i>
        </div>
        <div class="flex-grow-1">
            <h5 class="mb-1"><?= esc($method['name']) ?></h5>
            <p class="text-muted mb-0"><?= esc($method['description']) ?></p>
            <?php if (!$exists): ?>
                <small class="text-warning">Kích hoạt để sử dụng tính năng này</small>
            <?php endif; ?>
        </div>
        <div class="form-check form-switch">
            <input class="form-check-input payment-toggle" 
                   type="checkbox" 
                   data-method="<?= esc($method['payment_method']) ?>" 
                   <?= $isActive ? 'checked' : '' ?>>
        </div>
    </div>

<?php if (isset($linkUrl) && $linkUrl): ?>
    </a>
<?php else: ?>
    </div>
<?php endif; ?>
