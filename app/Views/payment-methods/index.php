<?= $this->extend('layouts/default') ?>

<?= $this->section('content') ?>
<div class="container my-3">
    <div>
        <h1 class="h3 text-gray-800"><PERSON><PERSON><PERSON><PERSON> thức thanh toán</h1>
        <p class="text-muted"><PERSON><PERSON><PERSON>n lý và kích hoạt các phương thức thanh toán cho cổng thanh toán của bạn</p>
    </div>

    <?php if (! $merchant): ?>
        <?= $this->include('payment-methods/components/merchant-pending') ?>
    <?php else: ?>
        <div class="mb-4">
            <div class="list-group list-group-flush">
                <?php
                $bankTransfer = array_filter($payment_methods, fn($m) => $m->payment_method === 'BANK_TRANSFER');
                $bankTransfer = reset($bankTransfer);
                $bankTransferExists = isset($payment_method_exists['BANK_TRANSFER']);

                $bankTransferMethod = [
                    'name' => 'Chuyển khoản ngân hàng',
                    'description' => 'Nhận thanh toán qua chuyển khoản trực tiếp',
                    'icon' => 'bi bi-bank',
                    'payment_method' => 'BANK_TRANSFER'
                ];

                echo $this->include('payment-methods/components/payment-method-item', [
                    'method' => $bankTransferMethod,
                    'exists' => $bankTransferExists,
                    'isActive' => $bankTransfer && $bankTransfer->active,
                    'linkUrl' => $bankTransferExists ? base_url('paymentmethods/banktransfer') : null
                ]);

                $card = array_filter($payment_methods, fn($m) => $m->payment_method === 'CARD');
                $card = reset($card);
                $cardExists = isset($payment_method_exists['CARD']);

                $cardMethod = [
                    'name' => 'Thẻ tín dụng/ghi nợ',
                    'description' => 'Chấp nhận thanh toán bằng thẻ quốc tế và nội địa',
                    'icon' => 'bi bi-credit-card',
                    'payment_method' => 'CARD'
                ];

                echo $this->include('payment-methods/components/payment-method-item', [
                    'method' => $cardMethod,
                    'exists' => $cardExists,
                    'isActive' => $card && $card->active,
                    'linkUrl' => null
                ]);
                ?>
            </div>
        </div>

        <div class="mb-4">
            <div class="mb-3">
                <h4>Tính năng</h4>
            </div>
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-3 mb-4">
                <?php foreach ($features as $feature): ?>
                    <?= $this->include('payment-methods/components/feature-card', ['feature' => $feature]) ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        $('.feature-toggle').on('click', function() {
            const featureId = $(this).data('feature');
            const action = $(this).data('action');
            const $button = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/toggleFeature') ?>',
                method: 'POST',
                beforeSend: function() {
                    $button.prop('disabled', true);
                },
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    feature_id: featureId,
                    action: action
                },
                success: function(response) {
                    if (response.status) {
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        } else {
                            notyf.success(response.message);
                            location.reload();
                        }
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false);
                }
            });
        });

        $('.payment-toggle').on('change', function() {
            const method = $(this).data('method');
            const action = $(this).is(':checked') ? 'enable' : 'disable';
            const $toggle = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/togglePaymentMethod') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    payment_method: method,
                    action: action
                },
                beforeSend: function() {
                    $toggle.prop('disabled', true);
                },
                success: function(response) {
                    if (!response.status) {
                        notyf.error(response.message);
                        $toggle.prop('checked', !$toggle.is(':checked'));
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                    $toggle.prop('checked', !$toggle.is(':checked'));
                },
                complete: function() {
                    $toggle.prop('disabled', false);
                }
            });
        });
    });
</script>
<?= $this->endSection() ?>
