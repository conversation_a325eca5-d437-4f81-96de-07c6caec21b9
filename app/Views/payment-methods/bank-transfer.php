<?= $this->extend('layouts/default') ?>

<?= $this->section('content') ?>

<div class="container my-3">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-2">
                    <li class="breadcrumb-item"><a href="<?= base_url('paymentmethods') ?>">Phương thức thanh toán</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Chuyển khoản ngân hàng</li>
                </ol>
            </nav>
            <h1 class="h3 text-gray-800">Chuyển khoản ngân hàng</h1>
            <p class="text-muted">Quản lý tài khoản thụ hưởng cho phương thức chuyển khoản ngân hàng</p>
        </div>
        <?php if (!empty($available_bank_accounts)): ?>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBankProfileModal">
                <i class="bi bi-plus-circle me-2"></i>Thêm tài khoản
            </button>
        <?php endif; ?>
    </div>

    <!-- Bank Profiles List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Tài khoản thụ hưởng (<?= count($bankProfiles) ?>)</h5>
        </div>
        <div class="list-group list-group-flush">
            <?php if (empty($bankProfiles)): ?>
                <div class="list-group-item text-center py-5">
                    <i class="bi bi-bank text-muted fs-1 mb-3"></i>
                    <h6 class="text-muted">Chưa có tài khoản thụ hưởng nào</h6>
                    <p class="text-muted mb-3">Thêm tài khoản ngân hàng để bắt đầu nhận thanh toán</p>
                    <?php if (!empty($available_bank_accounts)): ?>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addBankProfileModal">
                            <i class="bi bi-plus-circle me-2"></i>Thêm tài khoản đầu tiên
                        </button>
                    <?php else: ?>
                        <a href="<?= base_url('bankaccount/connect') ?>" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Kết nối tài khoản ngân hàng
                        </a>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <?php foreach ($bankProfiles as $profile): ?>
                    <?= $this->include('payment-methods/components/bank-profile-item', [
                        'profile' => $profile,
                        'isDefault' => $profile->default
                    ]) ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Include Modals -->
<?php if (!empty($available_bank_accounts)): ?>
    <?= $this->include('payment-methods/components/add-bank-profile-modal') ?>
<?php endif; ?>

<style>
    .badge {
        font-size: 0.6875rem;
        padding: 0.25em 0.5em;
    }

    @media (max-width: 575.98px) {
        .list-group-item .dropdown {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            z-index: 10;
        }
    }
</style>
