<?= $this->extend('layouts/default') ?>

<?= $this->section('content') ?>
<style>
    @media (max-width: 575.98px) {
        .list-group-item .d-flex.gap-2 {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .list-group-item .btn {
            width: 100% !important;
        }

        .list-group-item .d-flex.justify-content-end {
            justify-content: stretch !important;
        }

        .list-group-item {
            position: relative;
        }

        .list-group-item .dropdown {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            z-index: 10;
        }

        .list-group-item .d-flex.flex-column.flex-sm-row {
            gap: 0.75rem !important;
        }

        .list-group-item .flex-grow-1 {
            min-width: 0;
            flex: 1 1 auto;
        }

        .list-group-item .flex-shrink-0 {
            flex-shrink: 0;
        }
    }

    @media (min-width: 576px) {
        .list-group-item .dropdown {
            margin-left: auto;
        }
    }

    .va-selection {
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.6875rem;
        padding: 0.25em 0.5em;
    }

    .tab button {
        color: #000;
        border: none;
        background: transparent;
        padding: 0.25rem 1rem;
        border-radius: 0.3rem;
    }

    .tab button:hover {
        border: none !important;
    }

    .tab button.active {
        background-color: #fff;
        --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
        --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    }

    .form-check:has(input[name="connect_type_select"]:checked) {
        --bs-border-opacity: 1;
        border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
    }

    .nav-tabs .nav-link {
        color: #000;
        border: none;
        background: transparent;
        border-radius: 0.3rem;
        padding: 0.25rem 1rem;
    }

    .nav-tabs .nav-link.active {
        background-color: #fff;
        --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
        --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        color: #000;
    }

    body[data-theme='dark'] .nav-tabs .nav-link.active {
        background-color: #000;
        color: #fff;
    }

    body[data-theme='dark'] .nav-tabs .nav-link {
        color: #fff;
    }

    .form-selectgroup {
        display: inline-flex;
        margin: 0 -0.5rem -0.5rem 0;
        flex-wrap: wrap;
    }

    .form-selectgroup .form-selectgroup-item {
        margin: 0 0.5rem 0.5rem 0;
    }

    .form-selectgroup-vertical {
        flex-direction: column;
    }

    .form-selectgroup-item {
        display: block;
        position: relative;
    }

    .form-selectgroup-input {
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
        opacity: 0;
    }

    .form-selectgroup-label {
        position: relative;
        display: block;
        min-width: calc(1.25rem + 1.125rem + calc(var(--bs-border-width) * 2));
        margin: 0;
        padding: 0.5625rem 1rem;
        font-size: 0.875rem;
        line-height: 1.25rem;
        color: var(--bs-secondary);
        background: var(--bs-bg-forms);
        text-align: center;
        cursor: pointer;
        user-select: none;
        border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color);
        border-radius: var(--bs-border-radius);
        box-shadow: var(--bs-shadow-input);
        transition: border-color 0.3s, background 0.3s, color 0.3s;
    }

    @media (prefers-reduced-motion: reduce) {
        .form-selectgroup-label {
            transition: none;
        }
    }

    .form-selectgroup-label .icon:only-child {
        margin: 0 -0.25rem;
    }

    .form-selectgroup-label:hover {
        color: var(--bs-body-color);
    }

    .form-selectgroup-check {
        display: inline-block;
        width: 1.25rem;
        height: 1.25rem;
        border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color-translucent);
        vertical-align: middle;
        box-shadow: var(--bs-shadow-input);
    }

    .form-selectgroup-input[type=checkbox]+.form-selectgroup-label .form-selectgroup-check {
        border-radius: var(--bs-border-radius);
    }

    .form-selectgroup-input[type=radio]+.form-selectgroup-label .form-selectgroup-check {
        border-radius: 50%;
    }

    .form-selectgroup-input:checked+.form-selectgroup-label .form-selectgroup-check {
        background-color: var(--bs-primary);
        background-repeat: repeat;
        background-position: center;
        background-size: 1.25rem;
        border-color: var(--bs-border-color-translucent);
    }

    .form-selectgroup-input[type=checkbox]:checked+.form-selectgroup-label .form-selectgroup-check {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' width='16' height='16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8.5l2.5 2.5l5.5 -5.5'/%3e%3c/svg%3e");
    }

    .form-selectgroup-input[type=radio]:checked+.form-selectgroup-label .form-selectgroup-check {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3ccircle r='3' fill='%23ffffff' cx='8' cy='8' /%3e%3c/svg%3e");
    }

    .form-selectgroup-check-floated {
        position: absolute;
        top: 0.5625rem;
        right: 0.5625rem;
    }

    .form-selectgroup-input:checked+.form-selectgroup-label {
        z-index: 1;
        color: var(--bs-primary);
        background: rgba(var(--bs-primary-rgb), 0.04);
        border-color: var(--bs-primary);
    }

    .form-selectgroup-input:focus+.form-selectgroup-label {
        z-index: 2;
        color: var(--bs-primary);
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }
</style>

<div class="container my-3">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4 gap-3">
        <div class="flex-grow-1">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-2">
                    <li class="breadcrumb-item"><a href="<?= base_url('paymentmethods') ?>">Phương thức thanh toán</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Chuyển khoản ngân hàng</li>
                </ol>
            </nav>
            <h3 class="h3 text-gray-800 mb-0">Chuyển khoản ngân hàng</h3>
        </div>
    </div>

    <?php if (empty($bankProfiles)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card border-left-info shadow h-100 mb-4">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="bi bi-bank text-info" style="font-size: 4rem;"></i>
                        </div>
                        <h4 class="text-gray-800 mb-3">Chưa có tài khoản ngân hàng nào được kết nối</h4>
                        <p class="text-muted mb-4">
                            Để nhận thanh toán qua chuyển khoản ngân hàng, bạn cần kết nối ít nhất một tài khoản ngân hàng.
                            Tài khoản được đặt làm mặc định sẽ hiển thị trên cổng thanh toán SePay.
                        </p>
                        <a href="<?= base_url('bankaccount/connect') ?>" class="btn btn-primary btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>Kết nối tài khoản ngân hàng đầu tiên
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="d-flex justify-content-start mb-3">
            <ul class="nav nav-tabs nav-pills bg-light rounded-lg border-0 flex-nowrap overflow-auto" id="bankTransferTabs" role="tablist" style="max-width: 100%;">
                <li class="nav-item p-1 flex-shrink-0" role="presentation">
                    <button class="nav-link active" id="connected-tab" data-bs-toggle="tab" data-bs-target="#connected" type="button" role="tab" aria-controls="connected" aria-selected="true">
                        Tài khoản thụ hưởng
                        <code>(<?= count($bankProfiles) ?>)</code>
                    </button>
                </li>
                <?php
                $connectedAccountIds = array_column($bankProfiles, 'bank_account_id');
                $availableAccounts = array_filter($available_bank_accounts, function ($account) use ($connectedAccountIds) {
                    return ! in_array($account->id, $connectedAccountIds);
                });
                ?>
                <?php if (! empty($availableAccounts)): ?>
                    <li class="nav-item p-1 flex-shrink-0" role="presentation">
                        <button class="nav-link" id="available-tab" data-bs-toggle="tab" data-bs-target="#available" type="button" role="tab" aria-controls="available" aria-selected="false">
                            Tài khoản có sẵn
                            <code>(<?= count($availableAccounts) ?>)</code>
                        </button>
                    </li>
                <?php endif; ?>
            </ul>
        </div>

        <div class="tab-content" id="bankTransferTabContent">
            <div class="tab-pane fade show active" id="connected" role="tabpanel" aria-labelledby="connected-tab">
                <div class="list-group list-group-flush">
                    <?php foreach ($bankProfiles as $profile): ?>
                        <div class="list-group-item p-3">
                            <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-3">
                                <div class="d-flex align-items-start flex-grow-1 min-w-0">
                                    <div class="flex-shrink-0 me-3">
                                        <?php if ($profile->type === 'NAPAS_VIETQR'): ?>
                                            <img src="<?= base_url('assets/images/banklogo/napas247.png') ?>" alt="NAPAS VietQR" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                        <?php elseif (!empty($profile->logo_path)): ?>
                                            <img src="<?= base_url('assets/images/banklogo/' . $profile->logo_path) ?>" alt="<?= esc($profile->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                        <?php elseif (!empty($profile->icon_path)): ?>
                                            <img src="<?= base_url('assets/images/banklogo/' . $profile->icon_path) ?>" alt="<?= esc($profile->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                        <?php else: ?>
                                            <div class="bg-primary bg-opacity-10 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                                <i class="bi bi-bank text-primary fs-4"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1 min-w-0">
                                        <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center mb-1 gap-2">
                                            <h6 class="mb-0 text-truncate"><?= esc($profile->brand_name) ?></h6>
                                            <?php if ($profile->default): ?>
                                                <span class="badge bg-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Tài khoản này sẽ hiển thị trên cổng thanh toán SePay">
                                                    Mặc định
                                                    <i class="bi bi-info-circle ms-1"></i>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <p class="text-muted mb-0 text-break">
                                            <strong><?= esc($profile->account_number) ?></strong> - <?= esc($profile->account_holder_name) ?>
                                        </p>

                                        <?php if ($profile->type === 'BANK_ACCOUNT'): ?>
                                            <?php
                                            $banksWithoutVA = ['TPBank', 'VPBank', 'VietinBank'];
                                            $banksRequireVA = ['BIDV', 'MSB', 'KienLongBank', 'OCB'];
                                            $forceMainAccount = in_array($profile->brand_name, $banksWithoutVA);
                                            $requiresVA = in_array($profile->brand_name, $banksRequireVA);

                                            $canConfigureVA = !$forceMainAccount;
                                            ?>
                                            <?php if ($canConfigureVA): ?>
                                                <div class="va-status mt-2" data-profile-id="<?= $profile->id ?>">
                                                    <div class="va-loading d-none">
                                                        <small class="text-muted">
                                                            <div class="spinner-border spinner-border-sm me-1" style="width: 0.75rem; height: 0.75rem;"></div>
                                                            Đang kiểm tra VA...
                                                        </small>
                                                    </div>

                                                    <?php if ($requiresVA): ?>
                                                        <div class="text-warning va-required mb-0 d-none" style="font-size: 0.75rem;">
                                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                                            Ngân hàng này bắt buộc phải sử dụng tài khoản ảo
                                                        </div>
                                                    <?php endif; ?>

                                                    <div class="va-current mb-0 d-none" style="font-size: 0.75rem;">
                                                        <i class="bi bi-credit-card-2-front"></i>
                                                        <strong class="d-none d-sm-inline ms-1">Tài khoản ảo:</strong> <span class="mx-1 va-current-number"></span>(<span class="va-current-name"></span>)
                                                    </div>

                                                    <div class="va-dynamic-status mb-0 d-none" style="font-size: 0.75rem;">
                                                        <span class="text-primary"><strong>Tài khoản ảo động theo đơn hàng</strong></span>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <div class="mt-2">
                                                    <small class="text-muted" style="font-size: 0.75rem;">
                                                        <i class="bi bi-info-circle me-1"></i>
                                                        Ngân hàng này chỉ nhận thanh toán qua tài khoản chính
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <?php
                                $hasMenuItems = false;
                                if ($profile->type === 'BANK_ACCOUNT') {
                                    $banksWithoutVA = ['TPBank', 'VPBank', 'VietinBank'];
                                    $banksRequireVA = ['BIDV', 'MSB', 'KienLongBank', 'OCB'];
                                    $forceMainAccount = in_array($profile->brand_name, $banksWithoutVA);
                                    $requiresVA = in_array($profile->brand_name, $banksRequireVA);

                                    $canConfigureVA = !$forceMainAccount;
                                    if ($canConfigureVA) $hasMenuItems = true;
                                }
                                if (!$profile->default) $hasMenuItems = true;
                                if (!$profile->default && $profile->type === 'BANK_ACCOUNT') $hasMenuItems = true;
                                ?>
                                <?php if ($hasMenuItems): ?>
                                    <div class="dropdown flex-shrink-0">
                                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <?php if ($profile->type === 'BANK_ACCOUNT'): ?>
                                                <?php
                                                $banksWithoutVA = ['TPBank', 'VPBank', 'VietinBank'];
                                                $banksRequireVA = ['BIDV', 'MSB', 'KienLongBank', 'OCB'];
                                                $forceMainAccount = in_array($profile->brand_name, $banksWithoutVA);
                                                $requiresVA = in_array($profile->brand_name, $banksRequireVA);

                                                $canConfigureVA = !$forceMainAccount;
                                                ?>
                                                <?php if ($canConfigureVA): ?>
                                                    <li><a class="dropdown-item va-config-btn" href="#" data-profile-id="<?= $profile->id ?>" data-bank-account-id="<?= $profile->bank_account_id ?>" data-requires-va="<?= $requiresVA ? '1' : '0' ?>" data-bank-name="<?= esc($profile->brand_name) ?>">
                                                            <i class="bi bi-credit-card-2-front me-2"></i>Cấu hình tài khoản ảo
                                                            <?php if ($requiresVA): ?>
                                                                <span class="badge bg-warning text-dark ms-2">Bắt buộc</span>
                                                            <?php endif; ?>
                                                        </a></li>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                            <?php if (! $profile->default): ?>
                                                <li>
                                                    <a class="dropdown-item set-default-btn" href="#" data-profile-id="<?= $profile->id ?>">
                                                        <i class="bi bi-star me-2"></i>Đặt làm mặc định
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            <?php if (! $profile->default && $profile->type === 'BANK_ACCOUNT'): ?>
                                                <li>
                                                    <a class="dropdown-item text-danger remove-profile-btn" href="#" data-profile-id="<?= $profile->id ?>">
                                                        <i class="fas fa-link-slash me-2"></i>Gỡ khỏi tài khoản thụ hưởng
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <?php if (! empty($availableAccounts)): ?>
                <div class="tab-pane fade" id="available" role="tabpanel" aria-labelledby="available-tab">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center mb-3 gap-2">
                        <h6 class="mb-0">Danh sách tài khoản có thể thêm vào tài khoản thụ hưởng</h6>
                        <a href="<?= base_url('bankaccount/connect') ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-circle me-2"></i>Liên kết tài khoản ngân hàng mới
                        </a>
                    </div>
                    <div class="list-group list-group-flush">
                        <?php foreach ($availableAccounts as $account): ?>
                            <div class="list-group-item p-3">
                                <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center gap-3">
                                    <div class="d-flex align-items-start flex-grow-1 min-w-0">
                                        <div class="flex-shrink-0 me-3">
                                            <?php if (!empty($account->logo_path)): ?>
                                                <img src="<?= base_url('assets/images/banklogo/' . $account->logo_path) ?>" alt="<?= esc($account->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                            <?php elseif (! empty($account->icon_path)): ?>
                                                <img src="<?= base_url('assets/images/banklogo/' . $account->icon_path) ?>" alt="<?= esc($account->brand_name) ?>" class="rounded" style="width: 48px; height: 48px; object-fit: contain;">
                                            <?php else: ?>
                                                <div class="bg-secondary bg-opacity-10 rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                                    <i class="bi bi-bank text-secondary fs-4"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1 min-w-0">
                                            <h6 class="mb-1 text-truncate"><?= esc($account->brand_name) ?></h6>
                                            <p class="text-muted mb-0 text-break">
                                                <strong><?= esc($account->account_number) ?></strong> - <?= esc($account->account_holder_name) ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end flex-shrink-0">
                                        <?php
                                        $banksRequireVA = ['BIDV', 'MSB', 'KienLongBank', 'OCB'];
                                        $banksWithoutVA = ['TPBank', 'VPBank', 'VietinBank'];
                                        $requiresVA = in_array($account->brand_name, $banksRequireVA);
                                        $forceMainAccount = in_array($account->brand_name, $banksWithoutVA);


                                        ?>
                                        <?php if ($requiresVA): ?>
                                            <button type="button" class="btn btn-outline-success btn-sm add-profile-with-va-btn"
                                                data-account-id="<?= $account->id ?>"
                                                data-bank-name="<?= esc($account->brand_name) ?>"
                                                data-requires-va="1">
                                                <i class="bi bi-plus-circle me-1"></i><span class="d-none d-sm-inline">Thêm vào tài khoản thụ hưởng</span><span class="d-sm-none">Thêm</span>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="btn btn-outline-success btn-sm add-profile-btn" data-account-id="<?= $account->id ?>" data-bank-name="<?= esc($account->brand_name) ?>">
                                                <i class="bi bi-plus-circle me-1"></i><span class="d-none d-sm-inline">Thêm vào tài khoản thụ hưởng</span><span class="d-sm-none">Thêm</span>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<div class="modal fade" id="vaConfigModal" tabindex="-1" aria-labelledby="vaConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="vaConfigModalLabel">Cấu hình tài khoản ảo (VA)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="va-modal-loading text-center py-3 d-flex align-items-center justify-content-center">
                    <div class="spinner-border me-2"></div>
                    <span>Đang tải...</span>
                </div>

                <div class="va-modal-content d-flex flex-column gap-3">
                    <div class="bidv-dynamic-va-option d-none">
                        <div class="form-selectgroup form-selectgroup-boxes d-flex flex-column">
                            <label class="form-selectgroup-item flex-fill">
                                <input type="radio" name="va_type" id="dynamicVAOption" value="dynamic" class="form-selectgroup-input">
                                <div class="form-selectgroup-label d-flex align-items-center p-3">
                                    <div class="me-3">
                                        <span class="form-selectgroup-check"></span>
                                    </div>
                                    <div class="text-start flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <strong>Tài khoản ảo động theo đơn hàng</strong>
                                        </div>
                                        <small class="text-muted">Tự động tạo tài khoản ảo riêng cho từng đơn hàng</small>
                                    </div>
                                </div>
                            </label>
                            <label class="form-selectgroup-item flex-fill" id="staticVARadio">
                                <input type="radio" name="va_type" id="staticVAOption" value="static" class="form-selectgroup-input" checked>
                                <div class="form-selectgroup-label d-flex align-items-center p-3">
                                    <div class="me-3">
                                        <span class="form-selectgroup-check"></span>
                                    </div>
                                    <div class="text-start flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <strong>Tài khoản ảo cố định</strong>
                                        </div>
                                        <small class="text-muted">Sử dụng tài khoản ảo cố định</small>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="static-va-option">
                        <div>
                            <label class="form-label">Chọn tài khoản ảo</label>
                            <select class="form-select" id="vaModalSelect">
                                <option value="">Chọn tài khoản ảo...</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="va-modal-error d-none">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span class="va-modal-error-message"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="saveVAModal">Lưu cấu hình</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addProfileWithVAModal" tabindex="-1" aria-labelledby="addProfileWithVAModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProfileWithVAModalLabel">Thêm tài khoản thụ hưởng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="add-profile-va-loading d-none text-center py-3">
                    <div class="spinner-border me-2"></div>
                    <span>Đang tải...</span>
                </div>

                <div class="add-profile-va-content d-flex flex-column gap-3">
                    <div>
                        <label class="form-label">Ngân hàng</label>
                        <input type="text" class="form-control" id="addProfileBankName" readonly>
                    </div>

                    <div class="bidv-add-profile-dynamic-va-option d-none">
                        <div class="form-selectgroup form-selectgroup-boxes d-flex flex-column">
                            <label class="form-selectgroup-item flex-fill">
                                <input type="radio" name="add_va_type" id="addDynamicVAOption" value="dynamic" class="form-selectgroup-input" checked>
                                <div class="form-selectgroup-label d-flex align-items-center p-3">
                                    <div class="me-3">
                                        <span class="form-selectgroup-check"></span>
                                    </div>
                                    <div class="text-start flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <strong>Tài khoản ảo động theo đơn hàng</strong>
                                        </div>
                                        <small class="text-muted">Tự động tạo tài khoản ảo riêng cho từng đơn hàng</small>
                                    </div>
                                </div>
                            </label>
                            <label class="form-selectgroup-item flex-fill" id="addStaticVARadio">
                                <input type="radio" name="add_va_type" id="addStaticVAOption" value="static" class="form-selectgroup-input">
                                <div class="form-selectgroup-label d-flex align-items-center p-3">
                                    <div class="me-3">
                                        <span class="form-selectgroup-check"></span>
                                    </div>
                                    <div class="text-start flex-grow-1">
                                        <div class="d-flex align-items-center mb-1">
                                            <strong>Tài khoản ảo cố định</strong>
                                        </div>
                                        <small class="text-muted">Sử dụng tài khoản ảo cố định</small>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="add-static-va-option">
                        <div>
                            <label class="form-label">Chọn tài khoản ảo (VA) <span class="text-danger">*</span></label>
                            <select class="form-select" id="addProfileVASelect">
                                <option value="">Chọn tài khoản ảo...</option>
                            </select>
                            <div class="form-text">
                                Bắt buộc sử dụng tài khoản ảo để nhận thanh toán. Bạn có thể <a href="#" id="addNewVALink" target="_blank">thêm mới</a> tài khoản ảo.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="add-profile-va-error d-none">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span class="add-profile-va-error-message"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="addProfileWithVABtn">Thêm tài khoản thụ hưởng</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        loadAllVAStatus();

        function loadAllVAStatus() {
            $('.va-status .va-loading').removeClass('d-none');

            $.ajax({
                url: '<?= base_url('paymentmethods/getAllVAProfiles') ?>',
                method: 'GET',
                success: function(response) {
                    if (response.status && response.data) {
                        window.currentVAProfiles = response.data;

                        $('.va-status').each(function() {
                            const profileId = $(this).data('profile-id');
                            const $container = $(this);
                            const $loading = $container.find('.va-loading');
                            const $required = $container.find('.va-required');
                            const $current = $container.find('.va-current');

                            if (response.data[profileId]) {
                                const vaData = response.data[profileId];
                                const $dynamicStatus = $container.find('.va-dynamic-status');

                                if (vaData.use_dynamic_va) {
                                    $dynamicStatus.removeClass('d-none');
                                    $current.addClass('d-none');
                                    $required.addClass('d-none');
                                } else if (vaData.account_number) {
                                    $current.find('.va-current-number').text(vaData.account_number);
                                    $current.find('.va-current-name').text(vaData.account_holder_name);
                                    $current.removeClass('d-none');
                                    $required.addClass('d-none');
                                    $dynamicStatus.addClass('d-none');
                                } else {
                                    $current.addClass('d-none');
                                    $dynamicStatus.addClass('d-none');
                                    if ($required.length > 0) {
                                        $required.removeClass('d-none');
                                    }
                                }
                            } else {
                                const $dynamicStatus = $container.find('.va-dynamic-status');
                                $current.addClass('d-none');
                                $dynamicStatus.addClass('d-none');
                                if ($required.length > 0) {
                                    $required.removeClass('d-none');
                                }
                            }

                            $loading.addClass('d-none');
                        });
                    }
                },
                error: function() {
                    $('.va-status .va-loading').addClass('d-none');
                }
            });
        }

        function loadVAStatus(profileId) {
            const $container = $(`.va-status[data-profile-id="${profileId}"]`);
            const $loading = $container.find('.va-loading');
            const $required = $container.find('.va-required');
            const $current = $container.find('.va-current');

            $loading.removeClass('d-none');

            const vaData = window.currentVAProfiles ? window.currentVAProfiles[profileId] : null;

            if (vaData) {
                $current.find('.va-current-number').text(vaData.account_number);
                $current.find('.va-current-name').text(vaData.account_holder_name);
                $current.removeClass('d-none');
                $required.addClass('d-none');
            } else {
                $current.addClass('d-none');
                if ($required.length > 0) {
                    $required.removeClass('d-none');
                }
            }

            $loading.addClass('d-none');
        }

        $('.set-default-btn').on('click', function(e) {
            e.preventDefault();
            const profileId = $(this).data('profile-id');
            const $button = $(this);

            $.ajax({
                url: '<?= base_url('paymentmethods/setDefaultBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    profile_id: profileId
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload()
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="bi bi-star me-1"></i>');
                }
            });
        });

        $('.add-profile-btn').on('click', function() {
            const accountId = $(this).data('account-id');
            const bankName = $(this).data('bank-name');
            const $button = $(this);

            if (bankName === 'BIDV') {
                checkBidvEnterpriseAndPrompt(accountId, bankName, false, function(useDynamicVA) {
                    addBankProfile(accountId, useDynamicVA, null, $button);
                });
            } else {
                addBankProfile(accountId, false, null, $button);
            }
        });

        function checkBidvEnterpriseSupport(accountId, callback) {
            $.ajax({
                url: '<?= base_url('paymentmethods/checkBidvEnterpriseSupport') ?>',
                method: 'GET',
                data: {
                    bank_account_id: accountId
                },
                success: function(response) {
                    callback(response.status && response.supports_dynamic_va);
                },
                error: function() {
                    callback(false);
                }
            });
        }

        function checkBidvEnterpriseAndPrompt(accountId, bankName, requiresVA, callback) {
            checkBidvEnterpriseSupport(accountId, function(supportsDynamicVA) {
                if (supportsDynamicVA) {
                    showDynamicVAModal(accountId, bankName, requiresVA, callback);
                } else if (requiresVA) {
                    showVASelectionModal(accountId, bankName, callback);
                } else {
                    callback(false);
                }
            });
        }

        function showDynamicVAModal(accountId, bankName, requiresVA, callback) {
            const modalHtml = `
                <div class="modal fade" id="dynamicVAChoiceModal" tabindex="-1">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Chọn loại tài khoản ảo</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">Ngân hàng: <strong>${bankName}</strong></label>
                                </div>
                                <div class="form-selectgroup form-selectgroup-boxes d-flex flex-column">
                                    <label class="form-selectgroup-item flex-fill">
                                        <input type="radio" name="va_choice" value="dynamic" class="form-selectgroup-input" checked>
                                        <div class="form-selectgroup-label d-flex align-items-center p-3">
                                            <div class="me-3">
                                                <span class="form-selectgroup-check"></span>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="bi bi-lightning-charge text-primary me-2"></i>
                                                    <strong>VA động theo đơn hàng</strong>
                                                </div>
                                                <small class="text-muted">Tự động tạo VA riêng cho từng đơn hàng</small>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="form-selectgroup-item flex-fill">
                                        <input type="radio" name="va_choice" value="static" class="form-selectgroup-input">
                                        <div class="form-selectgroup-label d-flex align-items-center p-3">
                                            <div class="me-3">
                                                <span class="form-selectgroup-check"></span>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="bi bi-credit-card-2-front text-secondary me-2"></i>
                                                    <strong>VA cố định</strong>
                                                </div>
                                                <small class="text-muted">Sử dụng tài khoản ảo cố định</small>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                <button type="button" class="btn btn-primary" id="confirmVAChoice">Tiếp tục</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
            const $modal = $('#dynamicVAChoiceModal');
            $modal.modal('show');

            $modal.on('hidden.bs.modal', function() {
                $modal.remove();
            });

            $modal.on('change', 'input[name="va_choice"]', function() {
                const selectedValue = $(this).val();
                const $staticInfo = $modal.find('.static-va-info');

                if (selectedValue === 'static') {
                    if (!$staticInfo.length) {
                        $modal.find('.modal-body').append('<div class="static-va-info mt-3"><small class="text-muted"><i class="bi bi-info-circle me-1"></i>Sau khi chọn, bạn sẽ được yêu cầu chọn tài khoản ảo cố định.</small></div>');
                    }
                } else {
                    $staticInfo.remove();
                }
            });

            $('#confirmVAChoice').on('click', function() {
                const choice = $('input[name="va_choice"]:checked').val();
                $modal.modal('hide');

                if (choice === 'dynamic') {
                    callback(true);
                } else {
                    showVASelectionModal(accountId, bankName, function(useDynamicVA, vaId) {
                        callback(false, vaId);
                    });
                }
            });
        }

        function showVASelectionModal(accountId, bankName, callback) {
            $('#addProfileWithVAModal').data('account-id', accountId);
            $('#addProfileBankName').val(bankName);
            $('#addNewVALink').attr('href', '<?= base_url('bankaccount/details/') ?>/' + accountId);

            loadVAOptionsForAddProfile(accountId);
            $('#addProfileWithVAModal').modal('show');

            $('#addProfileWithVAModal').off('click', '#addProfileWithVABtn').on('click', '#addProfileWithVABtn', function() {
                const selectedVA = $('#addProfileVASelect').val();
                if (!selectedVA) {
                    notyf.error('Vui lòng chọn tài khoản ảo (VA)');
                    return;
                }

                $('#addProfileWithVAModal').modal('hide');
                callback(false, selectedVA);
            });
        }

        function addBankProfile(accountId, useDynamicVA, vaId, $button) {
            const data = {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                bank_account_id: accountId,
                is_default: <?= empty($bankProfiles) ? '1' : '0' ?>
            };

            if (useDynamicVA) {
                data.use_dynamic_va = '1';
            } else if (vaId) {
                data.bank_sub_account_id = vaId;
            }

            $.ajax({
                url: '<?= base_url('paymentmethods/addBankProfile') ?>',
                method: 'POST',
                data: data,
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm me-1 align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="bi bi-plus-circle me-1"></i>');
                }
            });
        }

        $('.add-profile-with-va-btn').on('click', function() {
            const accountId = $(this).data('account-id');
            const bankName = $(this).data('bank-name');
            const requiresVA = $(this).data('requires-va') == 1;

            $('#addProfileWithVAModal').data('account-id', accountId);
            $('#addProfileBankName').val(bankName);
            $('#addNewVALink').attr('href', '<?= base_url('bankaccount/details/') ?>/' + accountId);

            if (requiresVA) {
                if (bankName === 'BIDV') {
                    checkBidvEnterpriseForAddProfile(accountId, bankName);
                } else {
                    loadVAOptionsForAddProfile(accountId);
                }
            }

            $('#addProfileWithVAModal').modal('show');
        });

        function checkBidvEnterpriseForAddProfile(accountId, bankName) {
            checkBidvEnterpriseSupport(accountId, function(supportsDynamicVA) {
                const $modal = $('#addProfileWithVAModal');
                const $bidvOption = $modal.find('.bidv-add-profile-dynamic-va-option');
                const $staticOption = $('.add-static-va-option');

                if (supportsDynamicVA) {
                    $bidvOption.removeClass('d-none');
                    $('#addDynamicVAOption').prop('checked', true);
                    $staticOption.addClass('d-none');
                } else {
                    $bidvOption.addClass('d-none');
                    $staticOption.removeClass('d-none');
                }
                loadVAOptionsForAddProfile(accountId);
            });
        }

        function loadVAOptions(bankAccountId, selectElementId, onSuccess, onError) {
            $.ajax({
                url: '<?= base_url('paymentmethods/getBankSubAccounts') ?>',
                method: 'GET',
                data: {
                    bank_account_id: bankAccountId
                },
                success: function(response) {
                    if (response.status && response.data) {
                        const $select = $(selectElementId);
                        $select.empty().append('<option value="">Chọn tài khoản ảo...</option>');

                        response.data.forEach(function(va) {
                            const label = va.label ? ` (${va.label})` : '';
                            $select.append(`<option value="${va.id}">${va.account_number} - ${va.account_holder_name}${label}</option>`);
                        });

                        if (onSuccess) onSuccess(response.data);
                    } else {
                        if (onError) onError(response.message || 'Không thể tải danh sách VA');
                    }
                },
                error: function() {
                    if (onError) onError('Có lỗi xảy ra khi tải danh sách VA');
                }
            });
        }

        function loadVAOptionsForAddProfile(bankAccountId) {
            const $modal = $('#addProfileWithVAModal');
            const $loading = $modal.find('.add-profile-va-loading');
            const $content = $modal.find('.add-profile-va-content');

            $loading.removeClass('d-none');
            $content.addClass('d-none');

            loadVAOptions(bankAccountId, '#addProfileVASelect',
                function() {
                    $content.removeClass('d-none');
                    $loading.addClass('d-none');
                },
                function(message) {
                    showAddProfileVAError(message);
                    $loading.addClass('d-none');
                }
            );
        }

        function showAddProfileVAError(message) {
            const $modal = $('#addProfileWithVAModal');
            const $error = $modal.find('.add-profile-va-error');
            const $errorMessage = $error.find('.add-profile-va-error-message');

            $errorMessage.text(message);
            $error.removeClass('d-none');
        }

        $(document).on('change', 'input[name="add_va_type"]', function() {
            const $staticVAOption = $('.add-static-va-option');
            const selectedType = $(this).val();

            if (selectedType === 'static') {
                $staticVAOption.removeClass('d-none');
            } else {
                $staticVAOption.addClass('d-none');
            }
        });

        $('#addProfileWithVABtn').on('click', function() {
            const $modal = $('#addProfileWithVAModal');
            const accountId = $modal.data('account-id');
            const selectedVA = $('#addProfileVASelect').val();
            const isDynamicVA = $('#addDynamicVAOption').is(':checked');
            const $button = $(this);

            if (isDynamicVA) {} else {
                if (!selectedVA) {
                    notyf.error('Vui lòng chọn tài khoản ảo (VA)');
                    return;
                }
            }

            const data = {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                bank_account_id: accountId,
                is_default: <?= empty($bankProfiles) ? '1' : '0' ?>
            };

            if (isDynamicVA) {
                data.use_dynamic_va = '1';
            } else {
                data.bank_sub_account_id = selectedVA;
            }

            $.ajax({
                url: '<?= base_url('paymentmethods/addBankProfile') ?>',
                method: 'POST',
                data: data,
                beforeSend: function() {
                    $button.prop('disabled', true).text('Đang thêm...');
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        $modal.modal('hide');
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Thêm tài khoản thụ hưởng');
                }
            });
        });

        $('.remove-profile-btn').on('click', function(e) {
            e.preventDefault();
            const profileId = $(this).data('profile-id');
            const $button = $(this);

            if (!confirm('Bạn có chắc chắn muốn gỡ tài khoản này khỏi danh sách tài khoản thụ hưởng không?')) {
                return;
            }

            $.ajax({
                url: '<?= base_url('paymentmethods/removeBankProfile') ?>',
                method: 'POST',
                data: {
                    '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                    profile_id: profileId
                },
                beforeSend: function() {
                    $button.prop('disabled', true).find('i').replaceWith('<span class="spinner-border spinner-border-sm align-middle" role="status" aria-hidden="true"></span>');
                },
                success: function(response) {
                    if (response.status) {
                        location.reload();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).find('.spinner-border').replaceWith('<i class="fas fa-link-slash"></i>');
                }
            });
        });

        $('.va-config-btn').on('click', function(e) {
            e.preventDefault();
            const profileId = $(this).data('profile-id');
            const bankAccountId = $(this).data('bank-account-id');
            const requiresVA = $(this).data('requires-va') === '1';
            const bankName = $(this).data('bank-name');

            $('#vaConfigModal').data('profile-id', profileId);
            $('#vaConfigModal').data('bank-account-id', bankAccountId);
            $('#vaConfigModal').data('requires-va', requiresVA);
            $('#vaConfigModal').data('bank-name', bankName);

            loadVAOptionsToModal(profileId, bankAccountId, requiresVA, bankName);
            $('#vaConfigModal').modal('show');
        });

        function loadVAOptionsToModal(profileId, bankAccountId, requiresVA, bankName) {
            const $modal = $('#vaConfigModal');
            const $loading = $modal.find('.va-modal-loading');
            const $content = $modal.find('.va-modal-content');
            const $error = $modal.find('.va-modal-error');
            const $bidvOption = $modal.find('.bidv-dynamic-va-option');

            $loading.removeClass('d-none');
            $content.addClass('d-none');
            $error.addClass('d-none');

            if (bankName === 'BIDV') {
                checkBidvEnterpriseSupport(bankAccountId, function(supportsDynamicVA) {
                    if (supportsDynamicVA) {
                        $bidvOption.removeClass('d-none');
                    } else {
                        $bidvOption.addClass('d-none');
                    }
                    loadVAList();
                });
            } else {
                $bidvOption.addClass('d-none');
                loadVAList();
            }

            function loadVAList() {
                const $select = $('#vaModalSelect');

                loadVAOptions(bankAccountId, '#vaModalSelect',
                    function(data) {
                        $select.find('option:first').text(requiresVA ? 'Chọn tài khoản ảo...' : 'Không sử dụng VA');

                        loadCurrentVAToModal(profileId);
                        $content.removeClass('d-none');
                        $loading.addClass('d-none');
                    },
                    function(message) {
                        showVAModalError(message);
                        $loading.addClass('d-none');
                    }
                );
            }
        }

        function loadCurrentVAToModal(profileId) {
            const vaData = window.currentVAProfiles ? window.currentVAProfiles[profileId] : null;
            const $select = $('#vaModalSelect');
            const $dynamicOption = $('#dynamicVAOption');
            const $staticOption = $('#staticVAOption');
            const $staticVAOption = $('.static-va-option');

            if (vaData) {
                if (vaData.use_dynamic_va) {
                    $dynamicOption.prop('checked', true);
                    $staticOption.prop('checked', false);
                    $select.val('');
                    $staticVAOption.addClass('d-none');
                } else {
                    $dynamicOption.prop('checked', false);
                    $staticOption.prop('checked', true);
                    $select.val(vaData.id);
                    $staticVAOption.removeClass('d-none');
                }
            } else {
                $staticOption.prop('checked', true);
                $staticVAOption.removeClass('d-none');
            }
        }

        function showVAModalError(message) {
            const $modal = $('#vaConfigModal');
            const $error = $modal.find('.va-modal-error');
            const $errorMessage = $error.find('.va-modal-error-message');

            $errorMessage.text(message);
            $error.removeClass('d-none');
        }

        $(document).on('change', 'input[name="va_type"]', function() {
            const $staticVAOption = $('.static-va-option');
            const selectedType = $(this).val();

            if (selectedType === 'static') {
                $staticVAOption.removeClass('d-none');
            } else {
                $staticVAOption.addClass('d-none');
            }
        });

        $('#saveVAModal').on('click', function() {
            const $modal = $('#vaConfigModal');
            const profileId = $modal.data('profile-id');
            const selectedValue = $('#vaModalSelect').val();
            const isDynamicVA = $('#dynamicVAOption').is(':checked');
            const $button = $(this);

            const data = {
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>',
                profile_id: profileId
            };

            if (isDynamicVA) {
                data.use_dynamic_va = '1';
            } else {
                data.bank_sub_account_id = selectedValue || null;
                data.use_dynamic_va = '0';
            }

            $.ajax({
                url: '<?= base_url('paymentmethods/saveVAProfile') ?>',
                method: 'POST',
                data: data,
                beforeSend: function() {
                    $button.prop('disabled', true).text('Đang lưu...');
                },
                success: function(response) {
                    if (response.status) {
                        notyf.success(response.message);
                        $modal.modal('hide');
                        loadAllVAStatus();
                    } else {
                        notyf.error(response.message);
                    }
                },
                error: function() {
                    notyf.error('Có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Lưu cấu hình');
                }
            });
        });
    });
</script>
<?= $this->endSection() ?>