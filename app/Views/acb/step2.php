<link rel="stylesheet" href="<?php echo base_url(); ?>/assets/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url(); ?>/assets/css/rowReorder.bootstrap5.min.css">
<link rel="stylesheet" href="<?php echo base_url(); ?>/assets/css/responsive.dataTables.min.css">
<link rel="stylesheet" href="<?php echo base_url(); ?>/assets/tom-select/tom-select.bootstrap5.css">

<main class="content">
    <div class="container-fluid">
        <div class="row my-3 ">
            <div class="col-auto">
                <h3><i class="bi bi-plugin"></i> Kết nối ACB qua API</h3>
            </div>
            <div class="col-auto ms-auto text-end">
            </div>
        </div>

        <div class="card mt-3" style="width:100%">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-success border-bottom border-success">
                        <div class="my-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor"
                                class="bi bi-1-circle" viewBox="0 0 16 16">
                                <path
                                    d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Zm15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM9.283 4.002V12H7.971V5.338h-.065L6.072 6.656V5.385l1.899-1.383h1.312Z">
                                </path>
                            </svg>
                        </div>
                        <p>Điền số tài khoản</p>
                    </div>
                    <div class="col-md-3 text-success border-bottom border-success">
                        <div class="my-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor"
                                class="bi bi-2-circle" viewBox="0 0 16 16">
                                <path
                                    d="M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8Zm15 0A8 8 0 1 1 0 8a8 8 0 0 1 16 0ZM6.646 6.24v.07H5.375v-.064c0-1.213.879-2.402 2.637-2.402 1.582 0 2.613.949 2.613 2.215 0 1.002-.6 1.667-1.287 2.43l-.096.107-1.974 2.22v.077h3.498V12H5.422v-.832l2.97-3.293c.434-.475.903-1.008.903-1.705 0-.744-.557-1.236-1.313-1.236-.843 0-1.336.615-1.336 1.306Z">
                                </path>
                            </svg>
                        </div>
                        <p>Liên kết mở API</p>
                    </div>
                    <div class="col-md-3 text-muted">
                        <div class="my-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-3-circle" viewBox="0 0 16 16">
                                <path d="M7.918 8.414h-.879V7.342h.838c.78 0 1.348-.522 1.342-1.237 0-.709-.563-1.195-1.348-1.195-.79 0-1.312.498-1.348 1.055H5.275c.036-1.137.95-2.115 2.625-2.121 1.594-.012 2.608.885 2.637 2.062.023 1.137-.885 1.776-1.482 1.875v.07c.703.07 1.71.64 1.734 1.917.024 1.459-1.277 2.396-2.93 2.396-1.705 0-2.707-.967-2.754-2.144H6.33c.059.597.68 1.06 1.541 1.066.973.006 1.6-.563 1.588-1.354-.006-.779-.621-1.318-1.541-1.318"/>
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8"/>
                            </svg>
                        </div>
                        <p>Cấu hình</p>
                    </div>
                    <div class="col-md-3 text-muted">
                        <div class="my-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="bi bi-4-circle" viewBox="0 0 16 16">
                                <path d="M7.519 5.057q.33-.527.657-1.055h1.933v5.332h1.008v1.107H10.11V12H8.85v-1.559H4.978V9.322c.77-1.427 1.656-2.847 2.542-4.265ZM6.225 9.281v.053H8.85V5.063h-.065c-.867 1.33-1.787 2.806-2.56 4.218"/>
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0M1 8a7 7 0 1 0 14 0A7 7 0 0 0 1 8"/>
                            </svg>
                        </div> 
                        <p>Thử một giao dịch</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mx-auto" id="card-verify-otp" style="<?= $otp_request ? 'display: block;' : 'display: none;' ?>">
                    <div class="card-body">
                        <h5 class="card-title">Xác thực OTP</h5>
                        <div class="alert alert-primary" role="alert">
                            <div class="alert-message pb-0">
                                <p>Mã <b>OTP</b> đã gửi về số điện thoại <b><?= esc($bank_account_details->phone_number) ?></b> hoặc đã gửi OTP SafeKey tới ứng dụng ACB ONE BIZ. Vui lòng kiểm tra và điền mã
                                    OTP để xác nhận bạn chia sẻ biến động số dư qua API với SePay. Mã OTP sẽ hết hạn
                                    trong vòng 2 phút.</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <?= form_open(base_url("acb/ajax_step2"), 'id="step-2"') ?>
                                <div class="mx-auto" style="max-width:200px">
                                    <input type="hidden" name="id" value="<?= esc($bank_account_details->id);?>">

                                    <div class="mb-3 form-group">
                                        <label for="otp" class="form-label fw-bold">Điền mã OTP</label>
                                        <input type="text" class="form-control" name="otp" id="otp">
                                        <div class="invalid-feedback"></div>
                                    </div>

                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-save d-flex align-items-center mx-auto" style="gap: 0.25rem">
                                            <div class="spinner-border text-light loader" style="width: 16px; height: 16px; display: none;" role="status"></div>
                                            Liên kết API
                                        </button>
                                    </div>
                                </div>
                            <?= form_close() ?>
                            <hr>
                            <p>Trong trường hợp không nhận được mã OTP hoặc mã OTP hết hiệu lực, bạn có thể nhấn 
                                <button type="button" onclick="resendOTP(event)" class="btn btn-sm btn-outline-secondary rounded d-inline-flex align-items-center">
                                    <div class="spinner-border loader me-1" style="width: 14px; height: 14px; display: none;" role="status"></div>
                                    Gửi lại mã
                                </button>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="card mx-auto" id="card-request-otp" style="<?= !$otp_request ? 'display: block;' : 'display: none;' ?>">
                    <div class="card-body">
                        <h5 class="card-title">Gửi yêu cầu xác thực OTP</h5>
                        <div class="mt-3">
                            <p>Để xác nhận chia sẻ biến động số dư tài khoản
                            <b><?= esc($bank_account_details->account_number);?></b> tại
                            <b><?= esc($bank_account_details->brand_name);?></b>. Ngân hàng ACB sẽ gửi OTP sẽ gửi về số điện thoại <b><?= $bank_account_details->phone_number ?></b> hoặc hoặc gửi OTP SafeKey tới ứng dụng ACB ONE BIZ.</p>

                            <p>Quý khách vui lòng bấm lấy mã OTP để bắt đầu thực hiện.</p>
                        
                            <div class="text-center">
                                <?= form_open(base_url("acb/ajax_resend_otp"), 'class="form-resend-otp"') ?>
                                    <input type="hidden" name="id" value="<?= esc($bank_account_details->id);?>">
                                    <button type="button" onclick="resendOTP(event)" class="btn-submit d-inline-flex align-items-center btn btn-primary">
                                        <div class="spinner-border text-light loader me-2" style="width: 16px; height: 16px; display: none;" role="status"></div>
                                        Lấy mã OTP
                                    </button>
                                <?= form_close() ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mx-auto">
                    <div class="card-body d-flex align-items-center">
                        Bạn muốn dừng việc liên kết mở API?

                        <?= form_open(base_url("acb/ajax_unlink"), 'id="unlink" class="ms-auto"') ?>
                            <div class="mx-auto" style="max-width:200px">
                                <input type="hidden" name="id" value="<?= esc($bank_account_details->id);?>">

                                <button type="submit" class="btn btn-sm btn-outline-danger btn-save d-flex align-items-center mx-auto" style="gap: 0.25rem">
                                    <div class="spinner-border text-danger loader" style="width: 16px; height: 16px; display: none;" role="status"></div>
                                    Xóa tài khoản
                                </button>
                            </div>
                        <?= form_close() ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mx-auto">
                    <div class="card-body">
                        <h5 class="card-title">Hướng dẫn</h5>
                        
                        <div class="mt-3">
                            <p>Mục đích của bước này là xác nhận liên kết API cho tài khoản ngân hàng của bạn.</p>
                            <p>SePay là đơn vị ký đã kết hợp tác trực tiếp với ngân hàng ACB để triển khai các dịch vụ liên quan đến Open Banking:</p>
                            <ul class="lh-lg">
                                <li><a href="https://sepay.vn/acb.html" target="_blank">SePay chính thức hợp tác với ngân hàng ACB</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-5">
        <a class="btn btn-outline-primary rounded-pill py-2" href="https://docs.sepay.vn/ket-noi-acb.html"><i class="bi bi-question-circle"></i> Hướng dẫn kết nối ACB qua API</a>
    </div>
</main>

<?php include APPPATH . "Views/templates/autopay/inc_footer.php"; ?>

<script src="<?php echo base_url(); ?>/assets/js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery-3.5.1.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery.dataTables.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/dataTables.rowReorder.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/dataTables.responsive.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/notyf/notyf.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/app.js?v=1"></script>
<script src="<?php echo base_url(); ?>/assets/tom-select/tom-select.complete.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery.validate.min.js"></script>
<script src="<?php echo base_url(); ?>/assets/js/jquery.validate.additional-methods.min.js"></script>

<script>
let otpRequest = <?= json_encode($otp_request) ?>;

function resendOTP(event) {
    const btn = event.target

    $(btn).prop('disabled', true)
    $(btn).find('.loader').show();

    $.ajax({
        url: '<?= base_url('acb/ajax_resend_otp') ?>',
        method: 'POST',
        dataType: "JSON",
        data: $('.form-resend-otp').serialize(),
        success: (res, status, request) => {
            $(btn).prop('disabled', false)
            $(btn).find('.loader').hide();

            if (res.status == true) {
                otpRequest = res.otp_request
                notyf.success({
                    message: res.message,
                    dismissible: true
                });
                $('#card-request-otp').hide();
                $('#card-verify-otp').show();
            } else {
                notyf.error({
                    message: res.message,
                    dismissible: true
                });
            }
        },
        error: (err) => {
            $(btn).prop('disabled', false)
            $(btn).find('.loader').hide();

            if (err.status === 403) {
                notyf.error({
                    message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                    dismissible: true
                });
                return;
            }

            notyf.error({
                message: err.responseJSON.messages.error,
                dismissible: true
            });
        }
    })
}

$('input[name=otp]').on('change keyup keydown paste', () => {
    $('input[name=otp]').val($('input[name=otp]').val().replace(/[^0-9]/g, ""))
})

$(document).ready(() => {
    $('#step-2').validate({
        submitHandler: function(form) {
            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: $(form).serialize() + '&request_id=' + otpRequest.request_id + '&authorization_id=' + otpRequest.authorization_id,
                dataType: "JSON",
                success: (res, status, request) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (res.status == true) {
                        location.href = "<?php echo base_url('acb/settings') . '/' . $bank_account_details->id ;?>";
                    } else {
                        notyf.error({
                            message: res.message,
                            dismissible: true
                        });
                    }
                },
                error: (err) => {
                    $(form).find('.btn-save').prop('disabled', false)
                    $(form).find('.btn-save .loader').hide();

                    if (err.status === 400) {
                        for (const field in err.responseJSON.messages) {
                            $(`*[name=${field}]`).addClass('is-invalid')
                            $(`*[name=${field}]`).parent('.form-group').children('.invalid-feedback').html(err.responseJSON.messages[field])
                        }
                        return;
                    }

                    if (err.status === 403) {
                        notyf.error({
                            message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                            dismissible: true
                        });
                        return;
                    }

                    notyf.error({
                        message: err.responseJSON.messages.error,
                        dismissible: true
                    });
                }
            })
        },
        errorElement: 'div',
        rules: {
            "otp": {
                "required": true,
                "digits": true,
            },
        },
        messages: {
            otp: {
                required: 'Trường OTP là bắt buộc',
                digits: 'Trường OTP không hợp lệ'
            },
        },
        highlight: function (input) {
            $(input).addClass('is-invalid');
            $(input).parent('.input-group').addClass('is-invalid');
        },
        unhighlight: function (input) {
            $(input).removeClass('is-invalid');
            $(input).parent('.input-group').removeClass('is-invalid');
        },
        errorPlacement: function (error, input) {
            $(error).show()
            $(input).parents('.form-group').children('.invalid-feedback').append(error);
        }
    })

    $('#unlink').validate({
        submitHandler: function(form) {
            $(form).find('.btn-save').prop('disabled', true)
            $(form).find('.btn-save .loader').show();

            let confirmed = confirm('Bạn có chắc muốn xóa tài khoản ngân hàng này?')

            if (confirmed) {
                $.ajax({
                    url: $(form).attr('action'),
                    method: 'POST',
                    data: $(form).serialize(),
                    dataType: "JSON",
                    success: (res, status, request) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();
                 
                        if (res.status == true) {
                            location.href = "<?php echo base_url('bankaccount'); ?>";
                        } else {
                            notyf.error({
                                message: res.message,
                                dismissible: true
                            });
                        }
                    },
                    error: (err) => {
                        $(form).find('.btn-save').prop('disabled', false)
                        $(form).find('.btn-save .loader').hide();
                 
                        if (err.status === 403) {
                            notyf.error({
                                message: 'Đã có lỗi xảy ra, vui lòng tải lại trang',
                                dismissible: true
                            });
                            return;
                        }

                        notyf.error({
                            message: err.responseJSON.messages.error,
                            dismissible: true
                        });
                    }
                })
            }
        },
    })
})
</script>