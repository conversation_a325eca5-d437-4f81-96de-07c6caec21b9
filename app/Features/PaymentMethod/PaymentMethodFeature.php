<?php

namespace App\Features\PaymentMethod;

use Exception;
use App\Models\PgMerchantModel;
use App\Features\PaymentMethod\Contexts\MerchantContext;
use App\Features\PaymentMethod\Contexts\BankTransferContext;
use App\Features\PaymentMethod\Contexts\OnboardingContext;

class PaymentMethodFeature
{
    protected ?MerchantContext $merchantContext = null;

    protected ?BankTransferContext $bankTransferContext = null;

    protected ?OnboardingContext $onboardingContext = null;

    protected int $companyId;

    public function __construct(int $companyId)
    {
        $this->companyId = $companyId;
    }

    public function withMerchantContext(): self
    {
        try {
            $this->merchantContext = new MerchantContext($this, $this->companyId);
        } catch (Exception $e) {
            $this->merchantContext = null;
        }

        return $this;
    }

    public function ensureWithMerchantContext(): void
    {
        if (! $this->merchantContext) {
            throw new Exception('Merchant context is not set');
        }
    }

    public function merchantContext(): MerchantContext
    {
        $this->ensureWithMerchantContext();

        return $this->merchantContext;
    }

    public function withBankTransferContext(): self
    {
        $this->ensureWithMerchantContext();

        try {
            $this->bankTransferContext = new BankTransferContext($this, $this->merchantContext->merchant);
        } catch (Exception $e) {
            $this->bankTransferContext = null;
        }

        return $this;
    }

    public function ensureWithBankTransferContext(): void
    {
        if (! $this->bankTransferContext) {
            throw new Exception('Bank transfer context is not set');
        }
    }

    public function bankTransferContext(): BankTransferContext
    {
        $this->ensureWithBankTransferContext();

        return $this->bankTransferContext;
    }

    public function withOnboardingContext(): self
    {
        try {
            $this->onboardingContext = new OnboardingContext($this, $this->companyId);
        } catch (Exception $e) {
            $this->onboardingContext = null;
        }

        return $this;
    }

    public function ensureWithOnboardingContext(): void
    {
        if (! $this->onboardingContext) {
            throw new Exception('Onboarding context is not set');
        }
    }

    public function onboardingContext(): OnboardingContext
    {
        $this->ensureWithOnboardingContext();

        return $this->onboardingContext;
    }

    public function getMerchant(): ?object
    {
        return model(PgMerchantModel::class)
            ->where('company_id', $this->companyId)
            ->first();
    }

    public function jsonResponse(bool $status, ?string $message = null, $data = null): array
    {
        $response = ['status' => $status];

        if ($message) {
            $response['message'] = $message;
        }

        if ($data) {
            if (isset($data['errors'])) {
                $response['errors'] = $data['errors'];
            } else {
                $response = array_merge($response, $data);
            }
        }

        return $response;
    }
}
